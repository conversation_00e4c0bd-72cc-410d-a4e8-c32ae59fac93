#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to run the Streamlit sensor prediction simulator.
This script ensures all models are trained before launching the web app.
"""

import os
import sys
import subprocess
import streamlit as st

def check_models_exist():
    """Check if required model files exist"""
    required_files = [
        'models/best_ml_model.pkl',
        'models/lstm_model.h5',
        'models/scaling_values.pkl',
        'models/target_scaling.pkl'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    return missing_files

def train_models():
    """Train models by running main.py"""
    print("🔄 Training models... This may take a few minutes.")
    try:
        result = subprocess.run([sys.executable, 'main.py'], 
                              capture_output=True, text=True, check=True)
        print("✅ Models trained successfully!")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error training models: {e}")
        print(f"Error output: {e.stderr}")
        return False

def install_requirements():
    """Install required packages"""
    print("📦 Installing requirements...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '--user', '-r', 'requirements.txt'],
                      check=True)
        print("✅ Requirements installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        print("Trying to install essential packages individually...")
        essential_packages = ['streamlit', 'plotly', 'tensorflow', 'xgboost']
        for package in essential_packages:
            try:
                subprocess.run([sys.executable, '-m', 'pip', 'install', '--user', package],
                              check=True)
                print(f"✅ {package} installed successfully!")
            except subprocess.CalledProcessError:
                print(f"❌ Failed to install {package}")
        return True

def main():
    print("🚀 Starting Sensor Prediction Simulator Setup...")
    
    # Check if we're in the right directory
    if not os.path.exists('Feuil1-Table 1.csv'):
        print("❌ Error: Data file 'Feuil1-Table 1.csv' not found!")
        print("Please run this script from the project root directory.")
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        sys.exit(1)
    
    # Check if models exist
    missing_files = check_models_exist()
    
    if missing_files:
        print(f"⚠️  Missing model files: {missing_files}")
        print("Training models first...")
        
        if not train_models():
            print("❌ Failed to train models. Please check the error messages above.")
            sys.exit(1)
    else:
        print("✅ All model files found!")
    
    # Launch Streamlit app
    print("🌐 Launching Streamlit app...")
    print("The app will open in your default browser.")
    print("If it doesn't open automatically, go to: http://localhost:8501")
    print("\nPress Ctrl+C to stop the server.")
    
    try:
        subprocess.run([sys.executable, '-m', 'streamlit', 'run', 'streamlit_app.py'])
    except KeyboardInterrupt:
        print("\n👋 Shutting down the server. Goodbye!")
    except Exception as e:
        print(f"❌ Error running Streamlit: {e}")
        print("You can try running manually with: streamlit run streamlit_app.py")

if __name__ == "__main__":
    main()
