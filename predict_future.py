import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import joblib
import os
from tensorflow.keras.models import load_model

# Import preprocessing functions
from src.data.preprocessing import load_data

def predict_next_values(data, ml_model, steps=24, target_col='Active Power Mean'):
    """
    Predict the next values using the trained ML model
    """
    # Make a copy of the data
    future_data = data.copy()
    
    # Load scaling values
    min_vals, max_vals = joblib.load('models/scaling_values.pkl')
    target_min, target_max = joblib.load('models/target_scaling.pkl')
    
    predictions = []
    
    for _ in range(steps):
        # Prepare features for the current state
        current_features = future_data.iloc[-1:].copy()
        
        # Add time features
        current_features['hour'] = current_features.index.hour
        current_features['day'] = current_features.index.day
        current_features['day_of_week'] = current_features.index.dayofweek
        current_features['is_weekend'] = current_features['day_of_week'].apply(lambda x: 1 if x >= 5 else 0)
        
        # Add lag features
        for lag in [1, 2, 3]:
            if len(future_data) > lag:
                current_features[f'{target_col}_lag_{lag}'] = future_data[target_col].iloc[-lag]
            else:
                current_features[f'{target_col}_lag_{lag}'] = future_data[target_col].iloc[-1]
        
        # Scale features
        current_features_scaled = (current_features - min_vals) / (max_vals - min_vals)
        
        # Fill NaN values with 0
        current_features_scaled = current_features_scaled.fillna(0)
        
        # Make prediction
        prediction = ml_model.predict(current_features_scaled)[0]
        predictions.append(prediction)
        
        # Create a new row with the predicted value
        last_timestamp = future_data.index[-1]
        new_timestamp = last_timestamp + pd.Timedelta(minutes=15)  # Assuming 15-minute intervals
        
        new_row = pd.DataFrame({target_col: [prediction]}, index=[new_timestamp])
        
        # Append the new row to the data
        future_data = pd.concat([future_data, new_row])
    
    return predictions, future_data

def plot_predictions(original_data, future_data, predictions, target_col='Active Power Mean'):
    """
    Plot the original data and future predictions
    """
    plt.figure(figsize=(15, 8))
    
    # Plot original data
    plt.plot(original_data.index, original_data[target_col], label='Historical Data')
    
    # Plot predictions
    future_indices = future_data.index[-len(predictions):]
    plt.plot(future_indices, predictions, label='Predictions', linestyle='--')
    
    plt.title(f'Future {target_col} Predictions')
    plt.xlabel('Time')
    plt.ylabel(target_col)
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.savefig('results/future_predictions.png')
    plt.show()

def main():
    # Load the data
    data = load_data("Feuil1-Table 1.csv")
    
    # Load the best ML model
    best_ml_model = joblib.load('models/best_ml_model.pkl')
    
    # Predict future values (next 96 steps = 24 hours with 15-minute intervals)
    predictions, future_data = predict_next_values(data, best_ml_model, steps=96)
    
    # Plot predictions
    plot_predictions(data, future_data, predictions)
    
    # Save predictions to CSV
    future_indices = future_data.index[-len(predictions):]
    predictions_df = pd.DataFrame({
        'Timestamp': future_indices,
        'Predicted_Value': predictions
    })
    predictions_df.to_csv('results/future_predictions.csv', index=False)
    
    print(f"Predictions saved to results/future_predictions.csv")
    print(f"Plot saved to results/future_predictions.png")

if __name__ == "__main__":
    main()
