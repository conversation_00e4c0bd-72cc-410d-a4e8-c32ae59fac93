import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os
import tensorflow as tf
from tensorflow.keras.models import Sequential, load_model, save_model
from tensorflow.keras.layers import Dense, LSTM, GRU, Dropout, BatchNormalization
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
from tensorflow.keras.optimizers import <PERSON>
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

def create_lstm_model(input_shape, units=50, dropout_rate=0.2):
    """
    Create an LSTM model for time series forecasting
    """
    model = Sequential([
        LSTM(units, return_sequences=True, input_shape=input_shape),
        BatchNormalization(),
        Dropout(dropout_rate),
        
        LSTM(units, return_sequences=False),
        BatchNormalization(),
        Dropout(dropout_rate),
        
        Dense(20, activation='relu'),
        BatchNormalization(),
        
        Dense(1)
    ])
    
    model.compile(optimizer=<PERSON>(learning_rate=0.001), loss='mse')
    
    return model

def create_gru_model(input_shape, units=50, dropout_rate=0.2):
    """
    Create a GRU model for time series forecasting
    """
    model = Sequential([
        GRU(units, return_sequences=True, input_shape=input_shape),
        BatchNormalization(),
        Dropout(dropout_rate),
        
        GRU(units, return_sequences=False),
        BatchNormalization(),
        Dropout(dropout_rate),
        
        Dense(20, activation='relu'),
        BatchNormalization(),
        
        Dense(1)
    ])
    
    model.compile(optimizer=Adam(learning_rate=0.001), loss='mse')
    
    return model

def create_bidirectional_lstm_model(input_shape, units=50, dropout_rate=0.2):
    """
    Create a Bidirectional LSTM model for time series forecasting
    """
    model = Sequential([
        tf.keras.layers.Bidirectional(
            LSTM(units, return_sequences=True),
            input_shape=input_shape
        ),
        BatchNormalization(),
        Dropout(dropout_rate),
        
        tf.keras.layers.Bidirectional(
            LSTM(units, return_sequences=False)
        ),
        BatchNormalization(),
        Dropout(dropout_rate),
        
        Dense(20, activation='relu'),
        BatchNormalization(),
        
        Dense(1)
    ])
    
    model.compile(optimizer=Adam(learning_rate=0.001), loss='mse')
    
    return model

def train_dl_model(model, X_train, y_train, X_val, y_val, epochs=100, batch_size=32, model_name='model', save_dir='models'):
    """
    Train a deep learning model with early stopping and learning rate reduction
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # Define callbacks
    early_stopping = EarlyStopping(
        monitor='val_loss',
        patience=20,
        restore_best_weights=True
    )
    
    lr_reducer = ReduceLROnPlateau(
        monitor='val_loss',
        factor=0.5,
        patience=10,
        min_lr=1e-6
    )
    
    model_checkpoint = ModelCheckpoint(
        f'{save_dir}/{model_name}.h5',
        monitor='val_loss',
        save_best_only=True
    )
    
    # Train the model
    history = model.fit(
        X_train, y_train,
        validation_data=(X_val, y_val),
        epochs=epochs,
        batch_size=batch_size,
        callbacks=[early_stopping, lr_reducer, model_checkpoint],
        verbose=1
    )
    
    return model, history

def evaluate_dl_model(model, X_test, y_test, history=None, model_name='model', save_dir='results/plots'):
    """
    Evaluate a deep learning model and plot results
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # Make predictions
    y_pred = model.predict(X_test)
    
    # Calculate metrics
    mse = mean_squared_error(y_test, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    
    print(f"{model_name} - RMSE: {rmse:.2f}, MAE: {mae:.2f}, R2: {r2:.2f}")
    
    # Plot actual vs predicted
    plt.figure(figsize=(12, 6))
    plt.plot(y_test, label='Actual')
    plt.plot(y_pred, label='Predicted')
    plt.title(f'{model_name} - RMSE: {rmse:.2f}, R2: {r2:.2f}')
    plt.legend()
    plt.savefig(f'{save_dir}/{model_name}_predictions.png')
    plt.close()
    
    # Plot training history if available
    if history is not None:
        plt.figure(figsize=(12, 6))
        plt.plot(history.history['loss'], label='Training Loss')
        plt.plot(history.history['val_loss'], label='Validation Loss')
        plt.title(f'{model_name} - Training History')
        plt.xlabel('Epochs')
        plt.ylabel('Loss')
        plt.legend()
        plt.savefig(f'{save_dir}/{model_name}_history.png')
        plt.close()
    
    return {
        'Model': model_name,
        'MSE': mse,
        'RMSE': rmse,
        'MAE': mae,
        'R2': r2
    }

def train_multiple_dl_models(X_train, y_train, X_val, y_val, X_test, y_test, input_shape, save_dir='models'):
    """
    Train multiple deep learning models and evaluate them
    """
    # Define models to train
    models_config = {
        'LSTM': create_lstm_model(input_shape),
        'GRU': create_gru_model(input_shape),
        'BiLSTM': create_bidirectional_lstm_model(input_shape)
    }
    
    results = []
    trained_models = {}
    
    for name, model in models_config.items():
        print(f"Training {name} model...")
        
        # Train the model
        trained_model, history = train_dl_model(
            model, X_train, y_train, X_val, y_val,
            epochs=100, batch_size=32, model_name=name, save_dir=save_dir
        )
        
        # Evaluate the model
        eval_result = evaluate_dl_model(
            trained_model, X_test, y_test, history, model_name=name
        )
        
        results.append(eval_result)
        trained_models[name] = trained_model
    
    # Create a comparison plot
    results_df = pd.DataFrame(results)
    
    plt.figure(figsize=(12, 8))
    
    # Plot RMSE comparison
    plt.subplot(2, 1, 1)
    plt.bar(results_df['Model'], results_df['RMSE'])
    plt.title('RMSE Comparison')
    
    # Plot R2 comparison
    plt.subplot(2, 1, 2)
    plt.bar(results_df['Model'], results_df['R2'])
    plt.title('R2 Comparison')
    
    plt.tight_layout()
    plt.savefig(f'results/plots/dl_models_comparison.png')
    plt.close()
    
    return trained_models, results_df

def save_best_dl_model(results_df, save_dir='models'):
    """
    Identify and save information about the best deep learning model
    """
    # Find the best model based on RMSE
    best_model_name = results_df.loc[results_df['RMSE'].idxmin(), 'Model']
    
    # The model is already saved during training, just save the info
    with open(f'{save_dir}/best_dl_model_info.txt', 'w') as f:
        f.write(f"Best Model: {best_model_name}\n")
        f.write(f"RMSE: {results_df.loc[results_df['RMSE'].idxmin(), 'RMSE']}\n")
        f.write(f"R2: {results_df.loc[results_df['RMSE'].idxmin(), 'R2']}\n")
    
    print(f"Best model ({best_model_name}) info saved to {save_dir}/best_dl_model_info.txt")
    
    return best_model_name

def compare_ml_dl_models(ml_results, dl_results, save_dir='results/plots'):
    """
    Compare the best ML and DL models
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # Get the best ML and DL models
    best_ml = ml_results.loc[ml_results['RMSE'].idxmin()]
    best_dl = dl_results.loc[dl_results['RMSE'].idxmin()]
    
    # Create a comparison dataframe
    comparison = pd.DataFrame({
        'Model': ['Best ML: ' + best_ml['Model'], 'Best DL: ' + best_dl['Model']],
        'RMSE': [best_ml['RMSE'], best_dl['RMSE']],
        'R2': [best_ml['R2'], best_dl['R2']]
    })
    
    # Plot comparison
    plt.figure(figsize=(12, 10))
    
    # Plot RMSE comparison
    plt.subplot(2, 1, 1)
    plt.bar(comparison['Model'], comparison['RMSE'])
    plt.title('RMSE Comparison - ML vs DL')
    
    # Plot R2 comparison
    plt.subplot(2, 1, 2)
    plt.bar(comparison['Model'], comparison['R2'])
    plt.title('R2 Comparison - ML vs DL')
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/ml_dl_comparison.png')
    plt.close()
    
    # Save comparison to file
    comparison.to_csv(f'{save_dir}/ml_dl_comparison.csv', index=False)
    
    return comparison

if __name__ == "__main__":
    # This will be executed when the script is run directly
    print("Deep Learning models module loaded successfully!")
