import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Dense, LSTM, Dropout
from tensorflow.keras.callbacks import EarlyStopping
from sklearn.metrics import mean_squared_error, r2_score

def create_lstm_model(input_shape, units=50):
    """
    Create a simple LSTM model for time series forecasting
    """
    model = Sequential([
        LSTM(units, input_shape=input_shape),
        Dropout(0.2),
        Dense(20, activation='relu'),
        Dense(1)
    ])
    
    model.compile(optimizer='adam', loss='mse')
    
    return model

def prepare_sequence_data(X, y, sequence_length=10):
    """
    Prepare sequence data for LSTM model
    """
    X_seq, y_seq = [], []
    
    for i in range(len(X) - sequence_length):
        X_seq.append(X.iloc[i:i+sequence_length].values)
        y_seq.append(y.iloc[i+sequence_length])
    
    return np.array(X_seq), np.array(y_seq)

def train_lstm_model(X_train, y_train, X_test, y_test, sequence_length=10, epochs=50, batch_size=32):
    """
    Train an LSTM model for time series forecasting
    """
    # Prepare sequence data
    X_train_seq, y_train_seq = prepare_sequence_data(X_train, y_train, sequence_length)
    X_test_seq, y_test_seq = prepare_sequence_data(X_test, y_test, sequence_length)
    
    # Create model
    input_shape = (sequence_length, X_train.shape[1])
    model = create_lstm_model(input_shape)
    
    # Define early stopping
    early_stopping = EarlyStopping(
        monitor='val_loss',
        patience=10,
        restore_best_weights=True
    )
    
    # Train model
    history = model.fit(
        X_train_seq, y_train_seq,
        validation_data=(X_test_seq, y_test_seq),
        epochs=epochs,
        batch_size=batch_size,
        callbacks=[early_stopping],
        verbose=1
    )
    
    # Evaluate model
    y_pred = model.predict(X_test_seq)
    mse = mean_squared_error(y_test_seq, y_pred)
    rmse = np.sqrt(mse)
    r2 = r2_score(y_test_seq, y_pred)
    
    print(f"LSTM Model - RMSE: {rmse:.2f}, R2: {r2:.2f}")
    
    # Plot training history
    plt.figure(figsize=(10, 6))
    plt.plot(history.history['loss'], label='Training Loss')
    plt.plot(history.history['val_loss'], label='Validation Loss')
    plt.title('LSTM Model - Training History')
    plt.xlabel('Epochs')
    plt.ylabel('Loss')
    plt.legend()
    plt.savefig('results/plots/lstm_history.png')
    plt.close()
    
    # Plot predictions
    plt.figure(figsize=(12, 6))
    plt.plot(y_test_seq, label='Actual')
    plt.plot(y_pred, label='Predicted')
    plt.title(f'LSTM Model - RMSE: {rmse:.2f}, R2: {r2:.2f}')
    plt.legend()
    plt.savefig('results/plots/lstm_predictions.png')
    plt.close()
    
    # Save model
    model.save('models/lstm_model.h5')
    
    return model, rmse, r2

def compare_ml_dl(ml_rmse, ml_r2, dl_rmse, dl_r2, ml_name, save_dir='results/plots'):
    """
    Compare ML and DL models
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # Create comparison dataframe
    comparison = pd.DataFrame({
        'Model': [ml_name, 'LSTM'],
        'RMSE': [ml_rmse, dl_rmse],
        'R2': [ml_r2, dl_r2]
    })
    
    # Plot comparison
    plt.figure(figsize=(12, 10))
    
    # Plot RMSE comparison
    plt.subplot(2, 1, 1)
    plt.bar(comparison['Model'], comparison['RMSE'])
    plt.title('RMSE Comparison - ML vs DL')
    
    # Plot R2 comparison
    plt.subplot(2, 1, 2)
    plt.bar(comparison['Model'], comparison['R2'])
    plt.title('R2 Comparison - ML vs DL')
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/ml_dl_comparison.png')
    plt.close()
    
    # Save comparison to file
    comparison.to_csv(f'{save_dir}/ml_dl_comparison.csv', index=False)
    
    # Create summary report
    with open('results/ml_dl_summary.txt', 'w') as f:
        f.write("===== MODEL COMPARISON SUMMARY =====\n\n")
        
        f.write(f"ML Model: {ml_name}\n")
        f.write(f"RMSE: {ml_rmse:.4f}\n")
        f.write(f"R2: {ml_r2:.4f}\n\n")
        
        f.write("DL Model: LSTM\n")
        f.write(f"RMSE: {dl_rmse:.4f}\n")
        f.write(f"R2: {dl_r2:.4f}\n\n")
        
        if dl_rmse < ml_rmse:
            f.write("The Deep Learning model (LSTM) performed better based on RMSE.\n")
        else:
            f.write(f"The Machine Learning model ({ml_name}) performed better based on RMSE.\n")
    
    return comparison
