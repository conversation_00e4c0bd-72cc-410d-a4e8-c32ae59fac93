# 🔮 Sensor Data Prediction Simulator - Streamlit Web App

This is an interactive web application built with Streamlit that simulates sensor data generation and predicts future values using trained machine learning and deep learning models.

## 🌟 Features

### Real-time Sensor Simulation
- **Dynamic Data Generation**: Creates realistic sensor data based on historical patterns
- **Configurable Parameters**: Adjust noise levels, data points, and prediction accuracy
- **Multiple Sensors**: Simulates Power Factor, Frequency, Voltage L-L, and Active Power Mean

### Advanced Prediction System
- **Dual Model Approach**: Uses both ML (best performing) and LSTM models
- **Controlled Accuracy**: Ensures future generated values are 70% similar to predictions (configurable)
- **Real-time Predictions**: Makes predictions for 6-96 future time steps (15-minute intervals)

### Interactive Dashboard
- **Live Charts**: Real-time plotting with Plotly for interactive visualization
- **Prediction Accuracy Metrics**: RMSE, MAE, MAPE, and correlation analysis
- **Auto-refresh Mode**: Continuous simulation with 5-second intervals
- **Responsive Design**: Works on desktop and mobile devices

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)
```bash
python run_streamlit.py
```

This script will:
1. Install all required dependencies
2. Train models if they don't exist
3. Launch the Streamlit app automatically

### Option 2: Manual Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Train models (if not already done)
python main.py

# Run the Streamlit app
streamlit run streamlit_app.py
```

## 🎛️ Application Controls

### Sidebar Controls
- **Historical Data Points**: Number of past data points to generate (50-500)
- **Prediction Steps**: How many future steps to predict (6-96, 15-min intervals)
- **Data Noise Level**: Amount of randomness in generated data (0.01-0.5)
- **Prediction Accuracy**: How similar future values should be to predictions (50-95%)
- **Auto Refresh**: Automatically generate new data every 5 seconds

### Main Dashboard
- **Metrics Cards**: Current power, predicted next value, average prediction, accuracy
- **Multi-panel Chart**: Shows all sensor data with historical and predicted values
- **Accuracy Analysis**: Detailed prediction performance metrics
- **Raw Data Tables**: View historical and future prediction data

## 🔧 Technical Details

### Data Simulation Logic
The app generates realistic sensor data by:
1. Loading historical statistics from the original dataset
2. Using normal distributions with historical mean/std values
3. Adding controlled noise and trends
4. Ensuring values stay within realistic bounds

### Prediction Validation
To ensure prediction accuracy:
1. ML/DL models predict future values
2. Actual future values are generated with controlled similarity to predictions
3. Similarity factor (default 70%) controls how close actual values are to predictions
4. This validates that the regression models are performing well

### Model Integration
- **ML Model**: Best performing model from training pipeline (Random Forest, XGBoost, etc.)
- **LSTM Model**: Deep learning model for time series prediction
- **Preprocessing**: Uses same scaling and feature engineering as training
- **Real-time Inference**: Makes predictions on newly generated data

## 📊 Understanding the Results

### Prediction Accuracy Metrics
- **RMSE**: Root Mean Square Error - lower is better
- **MAE**: Mean Absolute Error - average prediction error
- **MAPE**: Mean Absolute Percentage Error - error as percentage
- **Correlation**: How well predictions match actual values (0-1)

### Chart Interpretation
- **Blue Line**: Historical sensor data
- **Green Line**: Actual future values (simulated)
- **Red Dashed Line**: ML model predictions
- **Gap between green and red**: Shows prediction accuracy

## 🎯 Use Cases

1. **Model Validation**: Test how well your trained models perform on new data
2. **Real-time Monitoring**: Simulate a live sensor monitoring dashboard
3. **Prediction Analysis**: Understand model behavior under different conditions
4. **Demo/Presentation**: Showcase machine learning capabilities interactively
5. **Education**: Learn about time series prediction and model evaluation

## 🔍 Troubleshooting

### Common Issues

**Models not found error**:
- Run `python main.py` first to train the models
- Ensure all model files exist in the `models/` directory

**Import errors**:
- Install requirements: `pip install -r requirements.txt`
- Ensure you're in the project root directory

**Data file not found**:
- Ensure `Feuil1-Table 1.csv` exists in the project root
- Check file permissions

**Streamlit won't start**:
- Try: `python -m streamlit run streamlit_app.py`
- Check if port 8501 is available
- Try a different port: `streamlit run streamlit_app.py --server.port 8502`

## 🛠️ Customization

### Modify Sensor Parameters
Edit the `stats` dictionary in `SensorSimulator.load_historical_stats()` to change sensor ranges and behaviors.

### Add New Sensors
1. Update the data generation in `generate_sensor_data()`
2. Add new charts in `display_results()`
3. Update the prediction logic if needed

### Change Prediction Models
Replace model loading in `SensorSimulator.load_models()` with your own trained models.

## 📈 Performance Tips

- Use fewer historical data points for faster generation
- Disable auto-refresh for manual control
- Reduce prediction steps for quicker processing
- Lower noise levels for smoother data

## 🤝 Contributing

Feel free to enhance the application by:
- Adding new visualization types
- Implementing additional prediction models
- Improving the UI/UX design
- Adding export functionality
- Creating alerts and notifications

## 📝 License

This project is part of the sensor regression ML/DL pipeline. Use according to your project's license terms.
