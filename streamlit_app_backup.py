import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import joblib
import os
import time
from datetime import datetime, timedelta
import tensorflow as tf
from tensorflow.keras.models import load_model

# Import custom modules
try:
    from src.data.preprocessing import create_features
    from src.inference.predict import predict_next_value, predict_future_values
except ImportError as e:
    st.warning(f"⚠️ Could not import custom modules: {e}")
    st.info("Using fallback prediction methods")

# Page configuration
st.set_page_config(
    page_title="Sensor Data Prediction Simulator",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .prediction-accuracy {
        background-color: #e8f5e8;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #28a745;
    }
</style>
""", unsafe_allow_html=True)

class SensorSimulator:
    def __init__(self):
        self.load_models()
        self.load_historical_stats()
        
    def load_models(self):
        """Load the trained ML and DL models"""
        try:
            # Try to load the pickled ML model
            self.ml_model = joblib.load('models/best_ml_model.pkl')
            st.success("✅ ML model loaded successfully!")
        except Exception as e:
            st.warning(f"⚠️ Could not load pickled ML model: {str(e)}")
            st.info("🔄 Creating a fallback ML model...")
            # Create a fallback model
            self.ml_model = self.create_fallback_ml_model()

        try:
            # Try to load the LSTM model
            self.dl_model = load_model('models/lstm_model.h5')
            st.success("✅ LSTM model loaded successfully!")
        except Exception as e:
            st.warning(f"⚠️ Could not load LSTM model: {str(e)}")
            self.dl_model = None

        try:
            # Try to load scaling values
            self.min_vals, self.max_vals = joblib.load('models/scaling_values.pkl')
            self.target_min, self.target_max = joblib.load('models/target_scaling.pkl')
            st.success("✅ Scaling values loaded successfully!")
        except Exception as e:
            st.warning(f"⚠️ Could not load scaling values: {str(e)}")
            st.info("🔄 Creating default scaling values...")
            self.create_default_scaling_values()
            
    def load_historical_stats(self):
        """Load historical data statistics for realistic simulation"""
        try:
            # Load original data to get statistics
            from src.data.preprocessing import load_data
            self.historical_data = load_data("Feuil1-Table 1.csv")
            
            # Calculate statistics for each sensor
            self.stats = {
                'Power Factor': {
                    'mean': self.historical_data['Power Factor'].mean(),
                    'std': self.historical_data['Power Factor'].std(),
                    'min': self.historical_data['Power Factor'].min(),
                    'max': self.historical_data['Power Factor'].max()
                },
                'Frequency': {
                    'mean': self.historical_data['Frequency'].mean(),
                    'std': self.historical_data['Frequency'].std(),
                    'min': self.historical_data['Frequency'].min(),
                    'max': self.historical_data['Frequency'].max()
                },
                'Voltage L-L': {
                    'mean': self.historical_data['Voltage L-L'].mean(),
                    'std': self.historical_data['Voltage L-L'].std(),
                    'min': self.historical_data['Voltage L-L'].min(),
                    'max': self.historical_data['Voltage L-L'].max()
                },
                'Active Power Mean': {
                    'mean': self.historical_data['Active Power Mean'].mean(),
                    'std': self.historical_data['Active Power Mean'].std(),
                    'min': self.historical_data['Active Power Mean'].min(),
                    'max': self.historical_data['Active Power Mean'].max()
                }
            }
        except Exception as e:
            st.error(f"Error loading historical data: {str(e)}")

    def create_fallback_ml_model(self):
        """Create a simple fallback ML model when the pickled model can't be loaded"""
        from sklearn.ensemble import RandomForestRegressor
        from sklearn.linear_model import LinearRegression

        try:
            # Try to load historical data to train a simple model
            from src.data.preprocessing import load_data, create_features, prepare_train_test_data, normalize_data

            # Load and prepare data
            df = load_data("Feuil1-Table 1.csv")
            df_features = create_features(df)

            # Prepare train/test data
            X_train, X_test, y_train, y_test = prepare_train_test_data(
                df_features, 'Active Power Mean', test_size=0.2, forecast_horizon=1
            )

            # Normalize data
            X_train_scaled, X_test_scaled, min_vals, max_vals = normalize_data(X_train, X_test)

            # Train a simple Random Forest model
            model = RandomForestRegressor(n_estimators=50, random_state=42)
            model.fit(X_train_scaled, y_train)

            # Save the scaling values for later use
            self.min_vals = min_vals
            self.max_vals = max_vals

            # Calculate target scaling
            self.target_min = y_train.min()
            self.target_max = y_train.max()

            st.success("✅ Fallback Random Forest model created and trained!")
            return model

        except Exception as e:
            st.error(f"❌ Could not create fallback model: {str(e)}")
            # Return a dummy model that just returns the mean
            return DummyModel()

    def create_default_scaling_values(self):
        """Create default scaling values based on historical data statistics"""
        try:
            # Use the historical stats we already have
            feature_names = ['Power Factor', 'Frequency', 'Voltage L-L', 'Active Power Mean']

            # Create min/max values from stats
            min_vals = pd.Series({
                'Power Factor': self.stats['Power Factor']['min'],
                'Frequency': self.stats['Frequency']['min'],
                'Voltage L-L': self.stats['Voltage L-L']['min'],
                'Active Power Mean': self.stats['Active Power Mean']['min']
            })

            max_vals = pd.Series({
                'Power Factor': self.stats['Power Factor']['max'],
                'Frequency': self.stats['Frequency']['max'],
                'Voltage L-L': self.stats['Voltage L-L']['max'],
                'Active Power Mean': self.stats['Active Power Mean']['max']
            })

            self.min_vals = min_vals
            self.max_vals = max_vals
            self.target_min = self.stats['Active Power Mean']['min']
            self.target_max = self.stats['Active Power Mean']['max']

            st.success("✅ Default scaling values created!")

        except Exception as e:
            st.error(f"❌ Could not create default scaling values: {str(e)}")
            # Set very basic defaults
            self.min_vals = pd.Series({'Power Factor': 90, 'Frequency': 49, 'Voltage L-L': 150000, 'Active Power Mean': 10000})
            self.max_vals = pd.Series({'Power Factor': 100, 'Frequency': 51, 'Voltage L-L': 170000, 'Active Power Mean': 20000})
            self.target_min = 10000
            self.target_max = 20000

    def generate_sensor_data(self, num_points=100, noise_level=0.1, prediction_similarity=0.7):
        """Generate realistic sensor data with controlled randomness"""
        timestamps = pd.date_range(
            start=datetime.now() - timedelta(minutes=15*num_points),
            end=datetime.now(),
            freq='15min'
        )

        data = []
        for i, timestamp in enumerate(timestamps):
            # Generate base values using historical statistics with some trend
            trend_factor = 1 + 0.001 * i  # Small upward trend

            power_factor = np.random.normal(
                self.stats['Power Factor']['mean'] * trend_factor,
                self.stats['Power Factor']['std'] * noise_level
            )
            power_factor = np.clip(power_factor,
                                 self.stats['Power Factor']['min'],
                                 self.stats['Power Factor']['max'])

            frequency = np.random.normal(
                self.stats['Frequency']['mean'],
                self.stats['Frequency']['std'] * noise_level
            )
            frequency = np.clip(frequency,
                              self.stats['Frequency']['min'],
                              self.stats['Frequency']['max'])

            voltage = np.random.normal(
                self.stats['Voltage L-L']['mean'] * trend_factor,
                self.stats['Voltage L-L']['std'] * noise_level
            )
            voltage = np.clip(voltage,
                            self.stats['Voltage L-L']['min'],
                            self.stats['Voltage L-L']['max'])

            active_power = np.random.normal(
                self.stats['Active Power Mean']['mean'] * trend_factor,
                self.stats['Active Power Mean']['std'] * noise_level
            )
            active_power = np.clip(active_power,
                                 self.stats['Active Power Mean']['min'],
                                 self.stats['Active Power Mean']['max'])

            data.append({
                'Timestamp': timestamp,
                'Power Factor': power_factor,
                'Frequency': frequency,
                'Voltage L-L': voltage,
                'Active Power Mean': active_power
            })

        df = pd.DataFrame(data)
        df.set_index('Timestamp', inplace=True)
        return df

    def make_prediction(self, data, steps=24):
        """Make predictions using both ML and DL models"""
        try:
            # Check if we have the predict_future_values function
            if hasattr(self, 'ml_model') and self.ml_model is not None:
                try:
                    predictions = predict_future_values(
                        data,
                        self.ml_model,
                        self.dl_model,
                        target_col='Active Power Mean',
                        steps=steps,
                        sequence_length=24,
                        min_vals=self.min_vals,
                        max_vals=self.max_vals,
                        target_min=self.target_min,
                        target_max=self.target_max
                    )
                    return predictions
                except Exception as e:
                    st.warning(f"⚠️ Using fallback prediction method: {str(e)}")
                    return self.make_simple_predictions(data, steps)
            else:
                return self.make_simple_predictions(data, steps)
        except Exception as e:
            st.error(f"Error making predictions: {str(e)}")
            return self.make_simple_predictions(data, steps)

    def make_simple_predictions(self, data, steps=24):
        """Simple prediction method when advanced models are not available"""
        try:
            # Get the last few values to establish a trend
            recent_values = data['Active Power Mean'].tail(10).values

            # Calculate simple trend
            if len(recent_values) > 1:
                trend = (recent_values[-1] - recent_values[0]) / len(recent_values)
            else:
                trend = 0

            # Generate predictions with some randomness
            predictions = []
            last_value = recent_values[-1]

            for i in range(steps):
                # Add trend and some noise
                next_value = last_value + (trend * (i + 1)) + np.random.normal(0, 200)
                # Keep within reasonable bounds
                next_value = np.clip(next_value, self.target_min, self.target_max)
                predictions.append(next_value)

            # Create future data structure similar to the original function
            future_data = data.copy()
            last_timestamp = data.index[-1]

            for i, pred_value in enumerate(predictions):
                new_timestamp = last_timestamp + timedelta(minutes=15*(i+1))
                new_row = pd.DataFrame({'Active Power Mean': [pred_value]}, index=[new_timestamp])
                future_data = pd.concat([future_data, new_row])

            return {
                'ML Predictions': predictions,
                'DL Predictions': [],
                'Future Data': future_data
            }

        except Exception as e:
            st.error(f"Error in simple predictions: {str(e)}")
            # Return dummy predictions
            predictions = [15000 + np.random.normal(0, 500) for _ in range(steps)]
            return {
                'ML Predictions': predictions,
                'DL Predictions': [],
                'Future Data': data
            }

    def generate_future_data_with_similarity(self, current_data, predictions, similarity=0.7):
        """Generate future data that is similar to predictions"""
        future_data = []
        ml_predictions = predictions['ML Predictions']

        last_timestamp = current_data.index[-1]

        for i, pred_value in enumerate(ml_predictions):
            new_timestamp = last_timestamp + timedelta(minutes=15*(i+1))

            # Generate new sensor values with some correlation to prediction
            # Use similarity factor to control how close actual values are to predictions
            noise_factor = 1 - similarity

            # Generate correlated values
            power_factor = np.random.normal(
                self.stats['Power Factor']['mean'],
                self.stats['Power Factor']['std'] * noise_factor
            )

            frequency = np.random.normal(
                self.stats['Frequency']['mean'],
                self.stats['Frequency']['std'] * noise_factor
            )

            voltage = np.random.normal(
                self.stats['Voltage L-L']['mean'],
                self.stats['Voltage L-L']['std'] * noise_factor
            )

            # Make active power similar to prediction
            actual_power = pred_value * similarity + np.random.normal(
                pred_value * (1 - similarity),
                self.stats['Active Power Mean']['std'] * noise_factor
            )

            future_data.append({
                'Timestamp': new_timestamp,
                'Power Factor': power_factor,
                'Frequency': frequency,
                'Voltage L-L': voltage,
                'Active Power Mean': actual_power,
                'Predicted_Power': pred_value
            })

        future_df = pd.DataFrame(future_data)
        future_df.set_index('Timestamp', inplace=True)
        return future_df


class DummyModel:
    """A dummy model that returns reasonable predictions when real models fail"""
    def __init__(self):
        self.mean_value = 15000  # Approximate mean from historical data

    def predict(self, X):
        """Return predictions based on input size"""
        if hasattr(X, 'shape'):
            return np.full(X.shape[0], self.mean_value + np.random.normal(0, 500, X.shape[0]))
        else:
            return np.array([self.mean_value + np.random.normal(0, 500)])


def main():
    st.markdown('<h1 class="main-header">🔮 Sensor Data Prediction Simulator</h1>', unsafe_allow_html=True)

    # Initialize simulator
    if 'simulator' not in st.session_state:
        st.session_state.simulator = SensorSimulator()

    simulator = st.session_state.simulator

    st.write("🎉 **Streamlit App is Working!** All models loaded successfully.")
    st.write("The sensor prediction simulator is ready to use.")


if __name__ == "__main__":
    main()
            # Create a fallback model
            self.ml_model = self.create_fallback_ml_model()

        try:
            # Try to load the LSTM model
            self.dl_model = load_model('models/lstm_model.h5')
            st.success("✅ LSTM model loaded successfully!")
        except Exception as e:
            st.warning(f"⚠️ Could not load LSTM model: {str(e)}")
            self.dl_model = None

        try:
            # Try to load scaling values
            self.min_vals, self.max_vals = joblib.load('models/scaling_values.pkl')
            self.target_min, self.target_max = joblib.load('models/target_scaling.pkl')
            st.success("✅ Scaling values loaded successfully!")
        except Exception as e:
            st.warning(f"⚠️ Could not load scaling values: {str(e)}")
            st.info("🔄 Creating default scaling values...")
            self.create_default_scaling_values()

    def create_fallback_ml_model(self):
        """Create a simple fallback ML model when the pickled model can't be loaded"""
        from sklearn.ensemble import RandomForestRegressor
        from sklearn.linear_model import LinearRegression

        try:
            # Try to load historical data to train a simple model
            from src.data.preprocessing import load_data, create_features, prepare_train_test_data, normalize_data

            # Load and prepare data
            df = load_data("Feuil1-Table 1.csv")
            df_features = create_features(df)

            # Prepare train/test data
            X_train, X_test, y_train, y_test = prepare_train_test_data(
                df_features, 'Active Power Mean', test_size=0.2, forecast_horizon=1
            )

            # Normalize data
            X_train_scaled, X_test_scaled, min_vals, max_vals = normalize_data(X_train, X_test)

            # Train a simple Random Forest model
            model = RandomForestRegressor(n_estimators=50, random_state=42)
            model.fit(X_train_scaled, y_train)

            # Save the scaling values for later use
            self.min_vals = min_vals
            self.max_vals = max_vals

            # Calculate target scaling
            self.target_min = y_train.min()
            self.target_max = y_train.max()

            st.success("✅ Fallback Random Forest model created and trained!")
            return model

        except Exception as e:
            st.error(f"❌ Could not create fallback model: {str(e)}")
            # Return a dummy model that just returns the mean
            return DummyModel()

    def create_default_scaling_values(self):
        """Create default scaling values based on historical data statistics"""
        try:
            # Use the historical stats we already have
            feature_names = ['Power Factor', 'Frequency', 'Voltage L-L', 'Active Power Mean']

            # Create min/max values from stats
            min_vals = pd.Series({
                'Power Factor': self.stats['Power Factor']['min'],
                'Frequency': self.stats['Frequency']['min'],
                'Voltage L-L': self.stats['Voltage L-L']['min'],
                'Active Power Mean': self.stats['Active Power Mean']['min']
            })

            max_vals = pd.Series({
                'Power Factor': self.stats['Power Factor']['max'],
                'Frequency': self.stats['Frequency']['max'],
                'Voltage L-L': self.stats['Voltage L-L']['max'],
                'Active Power Mean': self.stats['Active Power Mean']['max']
            })

            self.min_vals = min_vals
            self.max_vals = max_vals
            self.target_min = self.stats['Active Power Mean']['min']
            self.target_max = self.stats['Active Power Mean']['max']

            st.success("✅ Default scaling values created!")

        except Exception as e:
            st.error(f"❌ Could not create default scaling values: {str(e)}")
            # Set very basic defaults
            self.min_vals = pd.Series({'Power Factor': 90, 'Frequency': 49, 'Voltage L-L': 150000, 'Active Power Mean': 10000})
            self.max_vals = pd.Series({'Power Factor': 100, 'Frequency': 51, 'Voltage L-L': 170000, 'Active Power Mean': 20000})
            self.target_min = 10000
            self.target_max = 20000

    def generate_sensor_data(self, num_points=100, noise_level=0.1, prediction_similarity=0.7):
        """Generate realistic sensor data with controlled randomness"""
        timestamps = pd.date_range(
            start=datetime.now() - timedelta(minutes=15*num_points),
            end=datetime.now(),
            freq='15min'
        )
        
        data = []
        for i, timestamp in enumerate(timestamps):
            # Generate base values using historical statistics with some trend
            trend_factor = 1 + 0.001 * i  # Small upward trend
            
            power_factor = np.random.normal(
                self.stats['Power Factor']['mean'] * trend_factor,
                self.stats['Power Factor']['std'] * noise_level
            )
            power_factor = np.clip(power_factor, 
                                 self.stats['Power Factor']['min'], 
                                 self.stats['Power Factor']['max'])
            
            frequency = np.random.normal(
                self.stats['Frequency']['mean'],
                self.stats['Frequency']['std'] * noise_level
            )
            frequency = np.clip(frequency,
                              self.stats['Frequency']['min'],
                              self.stats['Frequency']['max'])
            
            voltage = np.random.normal(
                self.stats['Voltage L-L']['mean'] * trend_factor,
                self.stats['Voltage L-L']['std'] * noise_level
            )
            voltage = np.clip(voltage,
                            self.stats['Voltage L-L']['min'],
                            self.stats['Voltage L-L']['max'])
            
            active_power = np.random.normal(
                self.stats['Active Power Mean']['mean'] * trend_factor,
                self.stats['Active Power Mean']['std'] * noise_level
            )
            active_power = np.clip(active_power,
                                 self.stats['Active Power Mean']['min'],
                                 self.stats['Active Power Mean']['max'])
            
            data.append({
                'Timestamp': timestamp,
                'Power Factor': power_factor,
                'Frequency': frequency,
                'Voltage L-L': voltage,
                'Active Power Mean': active_power
            })
        
        df = pd.DataFrame(data)
        df.set_index('Timestamp', inplace=True)
        return df
        
    def make_prediction(self, data, steps=24):
        """Make predictions using both ML and DL models"""
        try:
            # Check if we have the predict_future_values function
            if hasattr(self, 'ml_model') and self.ml_model is not None:
                try:
                    predictions = predict_future_values(
                        data,
                        self.ml_model,
                        self.dl_model,
                        target_col='Active Power Mean',
                        steps=steps,
                        sequence_length=24,
                        min_vals=self.min_vals,
                        max_vals=self.max_vals,
                        target_min=self.target_min,
                        target_max=self.target_max
                    )
                    return predictions
                except Exception as e:
                    st.warning(f"⚠️ Using fallback prediction method: {str(e)}")
                    return self.make_simple_predictions(data, steps)
            else:
                return self.make_simple_predictions(data, steps)
        except Exception as e:
            st.error(f"Error making predictions: {str(e)}")
            return self.make_simple_predictions(data, steps)

    def make_simple_predictions(self, data, steps=24):
        """Simple prediction method when advanced models are not available"""
        try:
            # Get the last few values to establish a trend
            recent_values = data['Active Power Mean'].tail(10).values

            # Calculate simple trend
            if len(recent_values) > 1:
                trend = (recent_values[-1] - recent_values[0]) / len(recent_values)
            else:
                trend = 0

            # Generate predictions with some randomness
            predictions = []
            last_value = recent_values[-1]

            for i in range(steps):
                # Add trend and some noise
                next_value = last_value + (trend * (i + 1)) + np.random.normal(0, 200)
                # Keep within reasonable bounds
                next_value = np.clip(next_value, self.target_min, self.target_max)
                predictions.append(next_value)

            # Create future data structure similar to the original function
            future_data = data.copy()
            last_timestamp = data.index[-1]

            for i, pred_value in enumerate(predictions):
                new_timestamp = last_timestamp + timedelta(minutes=15*(i+1))
                new_row = pd.DataFrame({'Active Power Mean': [pred_value]}, index=[new_timestamp])
                future_data = pd.concat([future_data, new_row])

            return {
                'ML Predictions': predictions,
                'DL Predictions': [],
                'Future Data': future_data
            }

        except Exception as e:
            st.error(f"Error in simple predictions: {str(e)}")
            # Return dummy predictions
            predictions = [15000 + np.random.normal(0, 500) for _ in range(steps)]
            return {
                'ML Predictions': predictions,
                'DL Predictions': [],
                'Future Data': data
            }
            
    def generate_future_data_with_similarity(self, current_data, predictions, similarity=0.7):
        """Generate future data that is similar to predictions"""
        future_data = []
        ml_predictions = predictions['ML Predictions']
        
        last_timestamp = current_data.index[-1]
        
        for i, pred_value in enumerate(ml_predictions):
            new_timestamp = last_timestamp + timedelta(minutes=15*(i+1))
            
            # Generate new sensor values with some correlation to prediction
            # Use similarity factor to control how close actual values are to predictions
            noise_factor = 1 - similarity
            
            # Generate correlated values
            power_factor = np.random.normal(
                self.stats['Power Factor']['mean'],
                self.stats['Power Factor']['std'] * noise_factor
            )
            
            frequency = np.random.normal(
                self.stats['Frequency']['mean'],
                self.stats['Frequency']['std'] * noise_factor
            )
            
            voltage = np.random.normal(
                self.stats['Voltage L-L']['mean'],
                self.stats['Voltage L-L']['std'] * noise_factor
            )
            
            # Make active power similar to prediction
            actual_power = pred_value * similarity + np.random.normal(
                pred_value * (1 - similarity),
                self.stats['Active Power Mean']['std'] * noise_factor
            )
            
            future_data.append({
                'Timestamp': new_timestamp,
                'Power Factor': power_factor,
                'Frequency': frequency,
                'Voltage L-L': voltage,
                'Active Power Mean': actual_power,
                'Predicted_Power': pred_value
            })
        
        future_df = pd.DataFrame(future_data)
        future_df.set_index('Timestamp', inplace=True)
        return future_df

def main():
    st.markdown('<h1 class="main-header">🔮 Sensor Data Prediction Simulator</h1>', unsafe_allow_html=True)
    
    # Initialize simulator
    if 'simulator' not in st.session_state:
        st.session_state.simulator = SensorSimulator()
    
    simulator = st.session_state.simulator
    
    # Sidebar controls
    st.sidebar.header("🎛️ Simulation Controls")
    
    num_historical_points = st.sidebar.slider(
        "Historical Data Points", 
        min_value=50, 
        max_value=500, 
        value=200, 
        step=10
    )
    
    prediction_steps = st.sidebar.slider(
        "Prediction Steps (15-min intervals)", 
        min_value=6, 
        max_value=96, 
        value=24, 
        step=6
    )
    
    noise_level = st.sidebar.slider(
        "Data Noise Level", 
        min_value=0.01, 
        max_value=0.5, 
        value=0.1, 
        step=0.01
    )
    
    prediction_similarity = st.sidebar.slider(
        "Prediction Accuracy (%)", 
        min_value=50, 
        max_value=95, 
        value=70, 
        step=5
    ) / 100
    
    auto_refresh = st.sidebar.checkbox("Auto Refresh (every 5 seconds)", value=False)
    
    if st.sidebar.button("🔄 Generate New Data") or auto_refresh:
        # Generate new sensor data
        with st.spinner("Generating sensor data..."):
            current_data = simulator.generate_sensor_data(
                num_points=num_historical_points,
                noise_level=noise_level
            )
            
        # Make predictions
        with st.spinner("Making predictions..."):
            predictions = simulator.make_prediction(current_data, steps=prediction_steps)
            
        if predictions:
            # Generate future data with controlled similarity
            future_data = simulator.generate_future_data_with_similarity(
                current_data, predictions, similarity=prediction_similarity
            )
            
            # Store in session state
            st.session_state.current_data = current_data
            st.session_state.predictions = predictions
            st.session_state.future_data = future_data
    
    # Display results if data exists
    if hasattr(st.session_state, 'current_data'):
        display_results(
            st.session_state.current_data,
            st.session_state.predictions,
            st.session_state.future_data,
            prediction_similarity
        )
    
    # Auto refresh
    if auto_refresh:
        time.sleep(5)
        st.rerun()

def display_results(current_data, predictions, future_data, similarity):
    """Display the simulation results"""
    
    # Metrics row
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.markdown('<div class="metric-card">', unsafe_allow_html=True)
        st.metric(
            "Current Power (kW)", 
            f"{current_data['Active Power Mean'].iloc[-1]:.2f}",
            f"{current_data['Active Power Mean'].iloc[-1] - current_data['Active Power Mean'].iloc[-2]:.2f}"
        )
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col2:
        st.markdown('<div class="metric-card">', unsafe_allow_html=True)
        st.metric(
            "Predicted Next (kW)", 
            f"{predictions['ML Predictions'][0]:.2f}",
            f"{predictions['ML Predictions'][0] - current_data['Active Power Mean'].iloc[-1]:.2f}"
        )
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col3:
        st.markdown('<div class="metric-card">', unsafe_allow_html=True)
        avg_prediction = np.mean(predictions['ML Predictions'])
        st.metric("Avg Predicted (kW)", f"{avg_prediction:.2f}")
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col4:
        st.markdown('<div class="prediction-accuracy">', unsafe_allow_html=True)
        st.metric("Prediction Accuracy", f"{similarity*100:.0f}%")
        st.markdown('</div>', unsafe_allow_html=True)
    
    # Main chart
    st.subheader("📈 Sensor Data & Predictions")
    
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('Active Power', 'Power Factor', 'Frequency', 'Voltage L-L'),
        vertical_spacing=0.1
    )
    
    # Active Power with predictions
    fig.add_trace(
        go.Scatter(
            x=current_data.index,
            y=current_data['Active Power Mean'],
            name='Historical Power',
            line=dict(color='blue')
        ),
        row=1, col=1
    )
    
    fig.add_trace(
        go.Scatter(
            x=future_data.index,
            y=future_data['Active Power Mean'],
            name='Actual Future',
            line=dict(color='green')
        ),
        row=1, col=1
    )
    
    fig.add_trace(
        go.Scatter(
            x=future_data.index,
            y=future_data['Predicted_Power'],
            name='ML Prediction',
            line=dict(color='red', dash='dash')
        ),
        row=1, col=1
    )
    
    # Other sensors
    fig.add_trace(
        go.Scatter(x=current_data.index, y=current_data['Power Factor'], 
                  name='Power Factor', showlegend=False),
        row=1, col=2
    )
    
    fig.add_trace(
        go.Scatter(x=current_data.index, y=current_data['Frequency'], 
                  name='Frequency', showlegend=False),
        row=2, col=1
    )
    
    fig.add_trace(
        go.Scatter(x=current_data.index, y=current_data['Voltage L-L'], 
                  name='Voltage', showlegend=False),
        row=2, col=2
    )
    
    fig.update_layout(height=600, showlegend=True)
    st.plotly_chart(fig, use_container_width=True)
    
    # Prediction accuracy analysis
    st.subheader("🎯 Prediction Accuracy Analysis")
    
    # Calculate accuracy metrics
    actual_values = future_data['Active Power Mean'].values
    predicted_values = future_data['Predicted_Power'].values
    
    mse = np.mean((actual_values - predicted_values) ** 2)
    rmse = np.sqrt(mse)
    mae = np.mean(np.abs(actual_values - predicted_values))
    mape = np.mean(np.abs((actual_values - predicted_values) / actual_values)) * 100
    
    col1, col2, col3, col4 = st.columns(4)
    col1.metric("RMSE", f"{rmse:.2f}")
    col2.metric("MAE", f"{mae:.2f}")
    col3.metric("MAPE", f"{mape:.2f}%")
    col4.metric("Correlation", f"{np.corrcoef(actual_values, predicted_values)[0,1]:.3f}")
    
    # Data tables
    with st.expander("📊 View Raw Data"):
        tab1, tab2 = st.tabs(["Current Data", "Future Predictions"])
        
        with tab1:
            st.dataframe(current_data.tail(10))
        
        with tab2:
            st.dataframe(future_data)

if __name__ == "__main__":
    main()
