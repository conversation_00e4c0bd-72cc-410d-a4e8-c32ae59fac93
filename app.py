#!/usr/bin/env python3
"""
Real-Time Sensor Prediction System with Flask + WebSocket
True live streaming with automatic updates
"""

from flask import Flask, render_template
from flask_socketio import SocketIO, emit
import threading
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import sys
import os

# Add the current directory to Python path to import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our existing prediction functions
try:
    from predict import predict_future_values
    print("✅ Successfully imported predict_future_values")
except ImportError as e:
    print(f"⚠️ Could not import predict_future_values: {e}")
    predict_future_values = None

app = Flask(__name__)
app.config['SECRET_KEY'] = 'sensor_prediction_secret'
socketio = SocketIO(app, cors_allowed_origins="*")

class LiveSensorSystem:
    def __init__(self):
        self.running = False
        self.thread = None
        self.historical_data = pd.DataFrame()
        self.sensor_buffer = []
        self.max_historical_points = 100
        self.prediction_steps = 12  # 1 minute at 5-second intervals
        self.historical_baseline_created = False

        # Load models if available
        self.ml_model = None
        self.dl_model = None
        self.load_models()

        # Create stable historical baseline
        self.create_historical_baseline()
        
    def load_models(self):
        """Load ML/DL models if available"""
        try:
            import joblib
            import tensorflow as tf
            
            # Try to load models
            if os.path.exists('ml_model.pkl'):
                self.ml_model = joblib.load('ml_model.pkl')
                print("✅ ML model loaded")
            
            if os.path.exists('dl_model.h5'):
                self.dl_model = tf.keras.models.load_model('dl_model.h5')
                print("✅ DL model loaded")
                
        except Exception as e:
            print(f"⚠️ Could not load models: {e}")

    def create_historical_baseline(self):
        """Create stable historical baseline data that won't change"""
        print("📊 Creating stable historical baseline...")

        # Create 50 stable historical data points
        base_time = datetime.now() - timedelta(minutes=5)  # Start 5 minutes ago
        historical_points = []

        # Generate stable baseline with realistic patterns
        for i in range(50):
            timestamp = base_time + timedelta(seconds=5*i)

            # Create stable patterns (not random each time)
            time_factor = i / 50.0  # 0 to 1

            # Stable baseline values with slight trend
            power_factor = 95.0 + 2.0 * np.sin(time_factor * 2 * np.pi) + 0.5 * time_factor
            frequency = 50.0 + 0.1 * np.sin(time_factor * 3 * np.pi)
            voltage_ll = 160000 + 3000 * np.sin(time_factor * 1.5 * np.pi) + 1000 * time_factor
            active_power = 12000 + 2000 * np.sin(time_factor * 2.5 * np.pi) + 500 * time_factor

            historical_points.append({
                'timestamp': timestamp,
                'power_factor': power_factor,
                'frequency': frequency,
                'voltage_ll': voltage_ll,
                'active_power': active_power
            })

        # Convert to DataFrame
        self.historical_data = pd.DataFrame(historical_points)
        self.historical_data.set_index('timestamp', inplace=True)
        self.historical_baseline_created = True

        print(f"✅ Created {len(self.historical_data)} stable historical data points")
        print(f"📈 Power range: {self.historical_data['active_power'].min():.0f} - {self.historical_data['active_power'].max():.0f} kW")

    def generate_realistic_sensor_reading(self):
        """Generate realistic sensor reading based on last historical value"""
        current_time = datetime.now()

        # Get the last values from historical data to continue the trend
        if not self.historical_data.empty:
            last_values = self.historical_data.iloc[-1]

            # Generate new values based on last values with small variations
            power_factor = last_values['power_factor'] + np.random.normal(0, 0.3)  # Small change
            frequency = last_values['frequency'] + np.random.normal(0, 0.02)      # Very small change
            voltage_ll = last_values['voltage_ll'] + np.random.normal(0, 800)     # Moderate change
            active_power = last_values['active_power'] + np.random.normal(0, 300) # Moderate change

            # Apply realistic bounds
            power_factor = np.clip(power_factor, 90, 100)
            frequency = np.clip(frequency, 49.5, 50.5)
            voltage_ll = np.clip(voltage_ll, 150000, 170000)
            active_power = np.clip(active_power, 8000, 20000)
        else:
            # Fallback if no historical data
            power_factor = 95.0
            frequency = 50.0
            voltage_ll = 160000
            active_power = 12000

        reading = {
            'timestamp': current_time.isoformat(),
            'power_factor': float(power_factor),
            'frequency': float(frequency),
            'voltage_ll': float(voltage_ll),
            'active_power': float(active_power)
        }
        return reading
    
    def predict_all_sensors(self, data):
        """Predict future values for all sensors"""
        predictions = {}
        
        # Get the last few values for trend analysis
        recent_data = data.tail(10)
        
        # Predict each sensor
        sensors = ['power_factor', 'frequency', 'voltage_ll', 'active_power']
        for sensor in sensors:
            sensor_predictions = []
            last_value = recent_data[sensor].iloc[-1]
            
            # Calculate trend
            if len(recent_data) > 1:
                trend = (recent_data[sensor].iloc[-1] - recent_data[sensor].iloc[0]) / len(recent_data)
            else:
                trend = 0
            
            # Generate predictions
            current_value = last_value
            for i in range(self.prediction_steps):
                # Different prediction patterns for different sensors
                if sensor == 'power_factor':
                    noise = np.random.normal(0, 0.5)
                    cyclical = 0.3 * np.sin(i * 0.2)
                    bounds = (90, 100)
                elif sensor == 'frequency':
                    noise = np.random.normal(0, 0.02)
                    cyclical = 0.01 * np.sin(i * 0.3)
                    bounds = (49.5, 50.5)
                elif sensor == 'voltage_ll':
                    noise = np.random.normal(0, 1000)
                    cyclical = 500 * np.sin(i * 0.25)
                    bounds = (150000, 170000)
                else:  # active_power
                    noise = np.random.normal(0, 200)
                    cyclical = 150 * np.sin(i * 0.3)
                    bounds = (8000, 20000)
                
                # Calculate next value
                next_value = current_value + (trend * 0.1) + noise + cyclical
                next_value = np.clip(next_value, bounds[0], bounds[1])
                
                sensor_predictions.append(float(next_value))
                current_value = next_value * 0.9 + current_value * 0.1  # Smooth transition
            
            predictions[sensor] = sensor_predictions
        
        return predictions
    
    def sensor_loop(self):
        """Background loop that generates sensor data and predictions"""
        print("🚀 Starting live sensor system...")
        
        while self.running:
            try:
                # Generate new sensor reading
                new_reading = self.generate_realistic_sensor_reading()
                
                # Convert to DataFrame row
                new_row = pd.DataFrame([{
                    'timestamp': pd.to_datetime(new_reading['timestamp']),
                    'power_factor': new_reading['power_factor'],
                    'frequency': new_reading['frequency'],
                    'voltage_ll': new_reading['voltage_ll'],
                    'active_power': new_reading['active_power']
                }])
                new_row.set_index('timestamp', inplace=True)
                
                # Add ONLY the new data point to existing historical data
                # Historical data stays stable, we only append new readings
                self.historical_data = pd.concat([self.historical_data, new_row])

                # Keep only recent data (rolling window) - remove oldest if too many
                if len(self.historical_data) > self.max_historical_points:
                    self.historical_data = self.historical_data.tail(self.max_historical_points)
                
                # Generate predictions for all sensors
                predictions = self.predict_all_sensors(self.historical_data)
                
                # Create prediction timestamps
                last_time = self.historical_data.index[-1]
                pred_times = [(last_time + timedelta(seconds=5*(i+1))).isoformat() 
                             for i in range(self.prediction_steps)]
                
                # Prepare data for frontend
                historical_data_json = {
                    'timestamps': [t.isoformat() for t in self.historical_data.index],
                    'power_factor': self.historical_data['power_factor'].tolist(),
                    'frequency': self.historical_data['frequency'].tolist(),
                    'voltage_ll': self.historical_data['voltage_ll'].tolist(),
                    'active_power': self.historical_data['active_power'].tolist()
                }
                
                predictions_json = {
                    'timestamps': pred_times,
                    'power_factor': predictions['power_factor'],
                    'frequency': predictions['frequency'],
                    'voltage_ll': predictions['voltage_ll'],
                    'active_power': predictions['active_power']
                }
                
                # Emit to all connected clients
                socketio.emit('sensor_update', {
                    'historical': historical_data_json,
                    'predictions': predictions_json,
                    'latest_reading': new_reading,
                    'data_points': len(self.historical_data)
                })
                
                print(f"📊 Sent update: {new_reading['active_power']:.0f} kW at {new_reading['timestamp'][:19]}")
                
                # Wait for next reading (3 seconds)
                time.sleep(3)
                
            except Exception as e:
                print(f"❌ Error in sensor loop: {e}")
                break
        
        print("🛑 Live sensor system stopped")
    
    def start(self):
        """Start the live sensor system"""
        if not self.running:
            self.running = True
            self.thread = threading.Thread(target=self.sensor_loop, daemon=True)
            self.thread.start()
            print("✅ Live sensor system started")
    
    def stop(self):
        """Stop the live sensor system"""
        if self.running:
            self.running = False
            if self.thread:
                self.thread.join(timeout=1)
            print("✅ Live sensor system stopped")

# Global sensor system instance
sensor_system = LiveSensorSystem()

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    print('🔗 Client connected')
    emit('status', {'message': 'Connected to live sensor system'})

    # Send initial historical data if available
    if not sensor_system.historical_data.empty:
        # Generate initial predictions
        predictions = sensor_system.predict_all_sensors(sensor_system.historical_data)

        # Create prediction timestamps
        last_time = sensor_system.historical_data.index[-1]
        pred_times = [(last_time + timedelta(seconds=5*(i+1))).isoformat()
                     for i in range(sensor_system.prediction_steps)]

        # Prepare initial data
        historical_data_json = {
            'timestamps': [t.isoformat() for t in sensor_system.historical_data.index],
            'power_factor': sensor_system.historical_data['power_factor'].tolist(),
            'frequency': sensor_system.historical_data['frequency'].tolist(),
            'voltage_ll': sensor_system.historical_data['voltage_ll'].tolist(),
            'active_power': sensor_system.historical_data['active_power'].tolist()
        }

        predictions_json = {
            'timestamps': pred_times,
            'power_factor': predictions['power_factor'],
            'frequency': predictions['frequency'],
            'voltage_ll': predictions['voltage_ll'],
            'active_power': predictions['active_power']
        }

        # Send initial data
        emit('sensor_update', {
            'historical': historical_data_json,
            'predictions': predictions_json,
            'latest_reading': {
                'timestamp': sensor_system.historical_data.index[-1].isoformat(),
                'power_factor': sensor_system.historical_data['power_factor'].iloc[-1],
                'frequency': sensor_system.historical_data['frequency'].iloc[-1],
                'voltage_ll': sensor_system.historical_data['voltage_ll'].iloc[-1],
                'active_power': sensor_system.historical_data['active_power'].iloc[-1]
            },
            'data_points': len(sensor_system.historical_data)
        })

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    print('🔌 Client disconnected')

@socketio.on('start_sensors')
def handle_start_sensors():
    """Start the sensor system"""
    sensor_system.start()
    emit('status', {'message': 'Live sensors started'})

@socketio.on('stop_sensors')
def handle_stop_sensors():
    """Stop the sensor system"""
    sensor_system.stop()
    emit('status', {'message': 'Live sensors stopped'})

if __name__ == '__main__':
    print("🚀 Starting Real-Time Sensor Prediction System")
    print("📡 WebSocket server will handle live updates")
    print("🌐 Open http://localhost:5000 in your browser")
    
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
