import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import os

def calculate_metrics(y_true, y_pred):
    """
    Calculate regression metrics
    """
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    
    return {
        'MSE': mse,
        'RMSE': rmse,
        'MAE': mae,
        'R2': r2
    }

def plot_predictions(y_true, y_pred, model_name, save_dir='results/plots'):
    """
    Plot actual vs predicted values
    """
    os.makedirs(save_dir, exist_ok=True)
    
    plt.figure(figsize=(12, 6))
    plt.plot(y_true, label='Actual')
    plt.plot(y_pred, label='Predicted')
    plt.title(f'{model_name} - Actual vs Predicted')
    plt.legend()
    plt.savefig(f'{save_dir}/{model_name}_predictions.png')
    plt.close()
    
    # Plot scatter plot
    plt.figure(figsize=(8, 8))
    plt.scatter(y_true, y_pred, alpha=0.5)
    plt.plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], 'r--')
    plt.xlabel('Actual')
    plt.ylabel('Predicted')
    plt.title(f'{model_name} - Scatter Plot')
    plt.savefig(f'{save_dir}/{model_name}_scatter.png')
    plt.close()
    
    # Plot residuals
    residuals = y_true - y_pred
    plt.figure(figsize=(12, 6))
    plt.scatter(y_pred, residuals, alpha=0.5)
    plt.axhline(y=0, color='r', linestyle='--')
    plt.xlabel('Predicted')
    plt.ylabel('Residuals')
    plt.title(f'{model_name} - Residual Plot')
    plt.savefig(f'{save_dir}/{model_name}_residuals.png')
    plt.close()
    
    # Plot residual distribution
    plt.figure(figsize=(10, 6))
    sns.histplot(residuals, kde=True)
    plt.title(f'{model_name} - Residual Distribution')
    plt.savefig(f'{save_dir}/{model_name}_residual_dist.png')
    plt.close()

def plot_error_distribution(results_df, metric='RMSE', save_dir='results/plots'):
    """
    Plot error distribution across models
    """
    os.makedirs(save_dir, exist_ok=True)
    
    plt.figure(figsize=(12, 6))
    sns.barplot(x='Model', y=metric, data=results_df)
    plt.title(f'{metric} Comparison')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(f'{save_dir}/{metric}_comparison.png')
    plt.close()

def plot_feature_importance(model, feature_names, model_name, save_dir='results/plots'):
    """
    Plot feature importance for tree-based models
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # Check if model has feature_importances_ attribute
    if hasattr(model, 'feature_importances_'):
        # Get feature importances
        importances = model.feature_importances_
        
        # Sort feature importances in descending order
        indices = np.argsort(importances)[::-1]
        
        # Plot feature importances
        plt.figure(figsize=(12, 8))
        plt.title(f'Feature Importances - {model_name}')
        plt.bar(range(len(indices)), importances[indices], align='center')
        plt.xticks(range(len(indices)), [feature_names[i] for i in indices], rotation=90)
        plt.tight_layout()
        plt.savefig(f'{save_dir}/{model_name}_feature_importance.png')
        plt.close()
        
        # Return top 10 features
        top_features = [(feature_names[i], importances[i]) for i in indices[:10]]
        return top_features
    
    return None

def create_summary_report(ml_results, dl_results, save_dir='results'):
    """
    Create a summary report of all models
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # Combine ML and DL results
    ml_results['Type'] = 'Machine Learning'
    dl_results['Type'] = 'Deep Learning'
    all_results = pd.concat([ml_results, dl_results], ignore_index=True)
    
    # Find the best overall model
    best_model_idx = all_results['RMSE'].idxmin()
    best_model = all_results.loc[best_model_idx]
    
    # Create summary report
    with open(f'{save_dir}/summary_report.txt', 'w') as f:
        f.write("===== MODEL EVALUATION SUMMARY =====\n\n")
        
        f.write("--- Best Model ---\n")
        f.write(f"Model: {best_model['Model']} ({best_model['Type']})\n")
        f.write(f"RMSE: {best_model['RMSE']:.4f}\n")
        f.write(f"MAE: {best_model['MAE']:.4f}\n")
        f.write(f"R2: {best_model['R2']:.4f}\n\n")
        
        f.write("--- All Models Ranking (by RMSE) ---\n")
        ranked_models = all_results.sort_values('RMSE')
        for i, (_, row) in enumerate(ranked_models.iterrows()):
            f.write(f"{i+1}. {row['Model']} ({row['Type']}) - RMSE: {row['RMSE']:.4f}, R2: {row['R2']:.4f}\n")
    
    # Create comparison plots
    plt.figure(figsize=(14, 10))
    
    # Plot RMSE comparison
    plt.subplot(2, 1, 1)
    sns.barplot(x='Model', y='RMSE', hue='Type', data=all_results)
    plt.title('RMSE Comparison')
    plt.xticks(rotation=45)
    
    # Plot R2 comparison
    plt.subplot(2, 1, 2)
    sns.barplot(x='Model', y='R2', hue='Type', data=all_results)
    plt.title('R2 Comparison')
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/plots/all_models_comparison.png')
    plt.close()
    
    return best_model

if __name__ == "__main__":
    # This will be executed when the script is run directly
    print("Evaluation metrics module loaded successfully!")
