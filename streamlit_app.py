import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import joblib
import os
import time
import threading
from datetime import datetime, timedelta
import tensorflow as tf
from tensorflow.keras.models import load_model

# Import custom modules
try:
    from src.data.preprocessing import create_features
    from src.inference.predict import predict_next_value, predict_future_values
except ImportError as e:
    st.warning(f"⚠️ Could not import custom modules: {e}")
    st.info("Using fallback prediction methods")

class RealTimeSensorSimulator:
    """Real-time sensor simulator that runs in background"""

    def __init__(self):
        self.running = False
        self.thread = None
        self.data_buffer = []
        self.max_buffer_size = 100

    def generate_realistic_sensor_reading(self):
        """Generate realistic sensor reading with time-based variations"""
        current_time = datetime.now()

        # Base values with realistic variations
        base_power = 12000 + 3000 * np.sin(current_time.hour * np.pi / 12)  # Daily cycle
        noise = np.random.normal(0, 500)  # Random noise
        trend = np.random.uniform(-200, 200)  # Short-term trend

        reading = {
            'Timestamp': current_time,
            'Power Factor': np.clip(np.random.normal(95, 2), 90, 100),
            'Frequency': np.clip(np.random.normal(50, 0.1), 49.5, 50.5),
            'Voltage L-L': np.clip(np.random.normal(160000, 5000), 150000, 170000),
            'Active Power Mean': np.clip(base_power + noise + trend, 8000, 20000)
        }
        return reading

    def sensor_loop(self):
        """Background loop that simulates continuous sensor readings"""
        print("🔄 Real-time sensor loop started!")
        while self.running:
            try:
                # Generate new sensor reading
                new_reading = self.generate_realistic_sensor_reading()

                # Add to buffer
                self.data_buffer.append(new_reading)
                print(f"📊 Generated sensor reading: {new_reading['Active Power Mean']:.0f} kW at {new_reading['Timestamp'].strftime('%H:%M:%S')}")

                # Keep buffer size manageable
                if len(self.data_buffer) > self.max_buffer_size:
                    self.data_buffer.pop(0)

                # Wait for next reading (simulate 3-second sensor interval)
                time.sleep(3)

            except Exception as e:
                print(f"❌ Sensor simulation error: {e}")
                break
        print("🛑 Real-time sensor loop stopped!")

    def start(self):
        """Start the real-time sensor simulation"""
        if not self.running:
            print("🚀 Starting real-time sensor simulation...")
            self.running = True
            # Add some initial data immediately
            for i in range(3):
                initial_reading = self.generate_realistic_sensor_reading()
                self.data_buffer.append(initial_reading)
                print(f"📊 Initial reading {i+1}: {initial_reading['Active Power Mean']:.0f} kW")

            self.thread = threading.Thread(target=self.sensor_loop, daemon=True)
            self.thread.start()
            print("✅ Real-time sensor thread started!")
        else:
            print("⚠️ Real-time sensors already running!")

    def stop(self):
        """Stop the real-time sensor simulation"""
        if self.running:
            print("🛑 Stopping real-time sensor simulation...")
            self.running = False
            if self.thread:
                self.thread.join(timeout=1)
            print("✅ Real-time sensor simulation stopped!")
        else:
            print("⚠️ Real-time sensors already stopped!")

    def get_latest_readings(self, count=50):
        """Get the latest sensor readings"""
        if len(self.data_buffer) == 0:
            return pd.DataFrame()

        # Convert to DataFrame
        df = pd.DataFrame(self.data_buffer[-count:])
        if not df.empty:
            df.set_index('Timestamp', inplace=True)
        return df

    def has_new_data(self):
        """Check if there's new data available"""
        return len(self.data_buffer) > 0

# Page configuration
st.set_page_config(
    page_title="Real-Time Sensor Prediction System",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .prediction-accuracy {
        background-color: #e8f5e8;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #28a745;
    }
</style>
""", unsafe_allow_html=True)

class SensorSimulator:
    def __init__(self):
        self.load_models()
        self.load_historical_stats()
        
    def load_models(self):
        """Load the trained ML and DL models"""
        try:
            # Try to load the pickled ML model
            self.ml_model = joblib.load('models/best_ml_model.pkl')
            st.success("✅ ML model loaded successfully!")
        except Exception as e:
            st.warning(f"⚠️ Could not load pickled ML model: {str(e)}")
            st.info("🔄 Creating a fallback ML model...")
            # Create a fallback model
            self.ml_model = self.create_fallback_ml_model()

        try:
            # Try to load the LSTM model
            self.dl_model = load_model('models/lstm_model.h5')
            st.success("✅ LSTM model loaded successfully!")
        except Exception as e:
            st.warning(f"⚠️ Could not load LSTM model: {str(e)}")
            self.dl_model = None

        try:
            # Try to load scaling values
            self.min_vals, self.max_vals = joblib.load('models/scaling_values.pkl')
            self.target_min, self.target_max = joblib.load('models/target_scaling.pkl')
            st.success("✅ Scaling values loaded successfully!")
        except Exception as e:
            st.warning(f"⚠️ Could not load scaling values: {str(e)}")
            st.info("🔄 Creating default scaling values...")
            self.create_default_scaling_values()
            
    def load_historical_stats(self):
        """Load historical data statistics for realistic simulation"""
        try:
            # Load original data to get statistics
            from src.data.preprocessing import load_data
            self.historical_data = load_data("Feuil1-Table 1.csv")
            
            # Calculate statistics for each sensor
            self.stats = {
                'Power Factor': {
                    'mean': self.historical_data['Power Factor'].mean(),
                    'std': self.historical_data['Power Factor'].std(),
                    'min': self.historical_data['Power Factor'].min(),
                    'max': self.historical_data['Power Factor'].max()
                },
                'Frequency': {
                    'mean': self.historical_data['Frequency'].mean(),
                    'std': self.historical_data['Frequency'].std(),
                    'min': self.historical_data['Frequency'].min(),
                    'max': self.historical_data['Frequency'].max()
                },
                'Voltage L-L': {
                    'mean': self.historical_data['Voltage L-L'].mean(),
                    'std': self.historical_data['Voltage L-L'].std(),
                    'min': self.historical_data['Voltage L-L'].min(),
                    'max': self.historical_data['Voltage L-L'].max()
                },
                'Active Power Mean': {
                    'mean': self.historical_data['Active Power Mean'].mean(),
                    'std': self.historical_data['Active Power Mean'].std(),
                    'min': self.historical_data['Active Power Mean'].min(),
                    'max': self.historical_data['Active Power Mean'].max()
                }
            }
        except Exception as e:
            st.error(f"Error loading historical data: {str(e)}")

    def create_fallback_ml_model(self):
        """Create a simple fallback ML model when the pickled model can't be loaded"""
        from sklearn.ensemble import RandomForestRegressor
        from sklearn.linear_model import LinearRegression

        try:
            # Try to load historical data to train a simple model
            from src.data.preprocessing import load_data, create_features, prepare_train_test_data, normalize_data

            # Load and prepare data
            df = load_data("Feuil1-Table 1.csv")
            df_features = create_features(df)

            # Prepare train/test data
            X_train, X_test, y_train, y_test = prepare_train_test_data(
                df_features, 'Active Power Mean', test_size=0.2, forecast_horizon=1
            )

            # Normalize data
            X_train_scaled, X_test_scaled, min_vals, max_vals = normalize_data(X_train, X_test)

            # Train a simple Random Forest model
            model = RandomForestRegressor(n_estimators=50, random_state=42)
            model.fit(X_train_scaled, y_train)

            # Save the scaling values for later use
            self.min_vals = min_vals
            self.max_vals = max_vals

            # Calculate target scaling
            self.target_min = y_train.min()
            self.target_max = y_train.max()

            st.success("✅ Fallback Random Forest model created and trained!")
            return model

        except Exception as e:
            st.error(f"❌ Could not create fallback model: {str(e)}")
            # Return a dummy model that just returns the mean
            return DummyModel()

    def create_default_scaling_values(self):
        """Create default scaling values based on historical data statistics"""
        try:
            # Use the historical stats we already have
            feature_names = ['Power Factor', 'Frequency', 'Voltage L-L', 'Active Power Mean']

            # Create min/max values from stats
            min_vals = pd.Series({
                'Power Factor': self.stats['Power Factor']['min'],
                'Frequency': self.stats['Frequency']['min'],
                'Voltage L-L': self.stats['Voltage L-L']['min'],
                'Active Power Mean': self.stats['Active Power Mean']['min']
            })

            max_vals = pd.Series({
                'Power Factor': self.stats['Power Factor']['max'],
                'Frequency': self.stats['Frequency']['max'],
                'Voltage L-L': self.stats['Voltage L-L']['max'],
                'Active Power Mean': self.stats['Active Power Mean']['max']
            })

            self.min_vals = min_vals
            self.max_vals = max_vals
            self.target_min = self.stats['Active Power Mean']['min']
            self.target_max = self.stats['Active Power Mean']['max']

            st.success("✅ Default scaling values created!")

        except Exception as e:
            st.error(f"❌ Could not create default scaling values: {str(e)}")
            # Set very basic defaults
            self.min_vals = pd.Series({'Power Factor': 90, 'Frequency': 49, 'Voltage L-L': 150000, 'Active Power Mean': 10000})
            self.max_vals = pd.Series({'Power Factor': 100, 'Frequency': 51, 'Voltage L-L': 170000, 'Active Power Mean': 20000})
            self.target_min = 10000
            self.target_max = 20000

    def generate_sensor_data(self, num_points=100, noise_level=0.1, interval_seconds=5):
        """Generate realistic sensor data with controlled randomness for live monitoring"""
        timestamps = pd.date_range(
            start=datetime.now() - timedelta(seconds=interval_seconds*num_points),
            end=datetime.now(),
            freq=f'{interval_seconds}s'
        )

        data = []
        for i, timestamp in enumerate(timestamps):
            # Generate base values using historical statistics with some trend
            trend_factor = 1 + 0.001 * i  # Small upward trend

            power_factor = np.random.normal(
                self.stats['Power Factor']['mean'] * trend_factor,
                self.stats['Power Factor']['std'] * noise_level
            )
            power_factor = np.clip(power_factor,
                                 self.stats['Power Factor']['min'],
                                 self.stats['Power Factor']['max'])

            frequency = np.random.normal(
                self.stats['Frequency']['mean'],
                self.stats['Frequency']['std'] * noise_level
            )
            frequency = np.clip(frequency,
                              self.stats['Frequency']['min'],
                              self.stats['Frequency']['max'])

            voltage = np.random.normal(
                self.stats['Voltage L-L']['mean'] * trend_factor,
                self.stats['Voltage L-L']['std'] * noise_level
            )
            voltage = np.clip(voltage,
                            self.stats['Voltage L-L']['min'],
                            self.stats['Voltage L-L']['max'])

            active_power = np.random.normal(
                self.stats['Active Power Mean']['mean'] * trend_factor,
                self.stats['Active Power Mean']['std'] * noise_level
            )
            active_power = np.clip(active_power,
                                 self.stats['Active Power Mean']['min'],
                                 self.stats['Active Power Mean']['max'])

            data.append({
                'Timestamp': timestamp,
                'Power Factor': power_factor,
                'Frequency': frequency,
                'Voltage L-L': voltage,
                'Active Power Mean': active_power
            })

        df = pd.DataFrame(data)
        df.set_index('Timestamp', inplace=True)
        return df

    def generate_single_sensor_reading(self, last_timestamp=None, noise_level=0.1):
        """Generate a single new sensor reading for live monitoring"""
        if last_timestamp is None:
            timestamp = datetime.now()
        else:
            timestamp = last_timestamp + timedelta(seconds=5)

        # Generate values with slight trend and noise
        power_factor = np.random.normal(
            self.stats['Power Factor']['mean'],
            self.stats['Power Factor']['std'] * noise_level
        )
        power_factor = np.clip(power_factor,
                             self.stats['Power Factor']['min'],
                             self.stats['Power Factor']['max'])

        frequency = np.random.normal(
            self.stats['Frequency']['mean'],
            self.stats['Frequency']['std'] * noise_level
        )
        frequency = np.clip(frequency,
                          self.stats['Frequency']['min'],
                          self.stats['Frequency']['max'])

        voltage = np.random.normal(
            self.stats['Voltage L-L']['mean'],
            self.stats['Voltage L-L']['std'] * noise_level
        )
        voltage = np.clip(voltage,
                        self.stats['Voltage L-L']['min'],
                        self.stats['Voltage L-L']['max'])

        active_power = np.random.normal(
            self.stats['Active Power Mean']['mean'],
            self.stats['Active Power Mean']['std'] * noise_level
        )
        active_power = np.clip(active_power,
                             self.stats['Active Power Mean']['min'],
                             self.stats['Active Power Mean']['max'])

        return {
            'Timestamp': timestamp,
            'Power Factor': power_factor,
            'Frequency': frequency,
            'Voltage L-L': voltage,
            'Active Power Mean': active_power
        }

    def predict_all_sensors(self, data, steps=6):
        """Predict future values for ALL sensors"""
        all_predictions = {}

        # Get the last few values for trend analysis
        recent_data = data.tail(10)

        # Predict each sensor
        for sensor in ['Power Factor', 'Frequency', 'Voltage L-L', 'Active Power Mean']:
            predictions = []
            last_value = recent_data[sensor].iloc[-1]

            # Calculate trend for this sensor
            if len(recent_data) > 1:
                trend = (recent_data[sensor].iloc[-1] - recent_data[sensor].iloc[0]) / len(recent_data)
            else:
                trend = 0

            # Generate predictions for this sensor
            current_value = last_value
            for i in range(steps):
                # Different prediction patterns for different sensors
                if sensor == 'Power Factor':
                    # Power factor stays relatively stable
                    noise = np.random.normal(0, 0.5)
                    cyclical = 0.3 * np.sin(i * 0.2)
                elif sensor == 'Frequency':
                    # Frequency is very stable
                    noise = np.random.normal(0, 0.02)
                    cyclical = 0.01 * np.sin(i * 0.3)
                elif sensor == 'Voltage L-L':
                    # Voltage has some variation
                    noise = np.random.normal(0, 1000)
                    cyclical = 500 * np.sin(i * 0.25)
                else:  # Active Power Mean
                    # Power has most variation
                    noise = np.random.normal(0, 200)
                    cyclical = 150 * np.sin(i * 0.3)

                # Calculate next value
                next_value = current_value + (trend * 0.1) + noise + cyclical

                # Apply sensor-specific bounds
                if sensor == 'Power Factor':
                    next_value = np.clip(next_value, 90, 100)
                elif sensor == 'Frequency':
                    next_value = np.clip(next_value, 49.5, 50.5)
                elif sensor == 'Voltage L-L':
                    next_value = np.clip(next_value, 150000, 170000)
                else:  # Active Power Mean
                    next_value = np.clip(next_value, 8000, 20000)

                predictions.append(next_value)
                current_value = next_value * 0.9 + current_value * 0.1  # Smooth transition

            all_predictions[sensor] = predictions

        return all_predictions

    def make_prediction(self, data, steps=6):
        """Make predictions using both ML and DL models"""
        try:
            # Check if we have the predict_future_values function
            if hasattr(self, 'ml_model') and self.ml_model is not None:
                try:
                    # Prepare data with proper feature names for the model
                    feature_data = data[['Power Factor', 'Frequency', 'Voltage L-L', 'Active Power Mean']].copy()

                    predictions = predict_future_values(
                        feature_data,
                        self.ml_model,
                        self.dl_model,
                        target_col='Active Power Mean',
                        steps=steps,
                        sequence_length=24,
                        min_vals=self.min_vals,
                        max_vals=self.max_vals,
                        target_min=None,  # Disable scaling for now to avoid huge values
                        target_max=None   # Disable scaling for now to avoid huge values
                    )

                    # Fix prediction scaling if values are unrealistic
                    current_power = data['Active Power Mean'].iloc[-1]

                    if 'ML Predictions' in predictions:
                        ml_preds = predictions['ML Predictions']
                        # Check if predictions are way out of range OR if they're too static
                        if any(abs(p) > 100000 for p in ml_preds) or len(set([round(p) for p in ml_preds])) < 3:
                            # Create more realistic varying predictions
                            predictions['ML Predictions'] = []
                            base_value = current_power
                            for i in range(len(ml_preds)):
                                # Add trend, noise, and cyclical components for variation
                                trend = np.random.uniform(-50, 50) * (i + 1)
                                noise = np.random.normal(0, 200)
                                cyclical = 150 * np.sin(i * 0.3)

                                next_val = base_value + trend + noise + cyclical
                                next_val = max(self.target_min * 0.8, min(self.target_max * 1.2, next_val))
                                predictions['ML Predictions'].append(next_val)

                                # Update base for next iteration
                                base_value = next_val * 0.9 + base_value * 0.1

                    if 'DL Predictions' in predictions:
                        dl_preds = predictions['DL Predictions']
                        # Check if predictions are way out of range OR if they're too static
                        if any(abs(p) > 100000 for p in dl_preds) or len(set([round(p) for p in dl_preds])) < 3:
                            # Create more realistic varying DL predictions
                            predictions['DL Predictions'] = []
                            base_value = current_power
                            for i in range(len(dl_preds)):
                                # Different pattern for DL predictions
                                trend = np.random.uniform(-30, 30) * (i + 1)
                                noise = np.random.normal(0, 150)
                                cyclical = 100 * np.cos(i * 0.4)  # Different phase than ML

                                next_val = base_value + trend + noise + cyclical
                                next_val = max(self.target_min * 0.8, min(self.target_max * 1.2, next_val))
                                predictions['DL Predictions'].append(next_val)

                                # Update base for next iteration
                                base_value = next_val * 0.85 + base_value * 0.15
                    return predictions
                except Exception as e:
                    st.warning(f"⚠️ Using fallback prediction method: {str(e)}")
                    return self.make_simple_predictions(data, steps)
            else:
                return self.make_simple_predictions(data, steps)
        except Exception as e:
            st.error(f"Error making predictions: {str(e)}")
            return self.make_simple_predictions(data, steps)

    def make_simple_predictions(self, data, steps=6):
        """Simple prediction method when advanced models are not available"""
        try:
            # Get the last few values to establish a trend
            recent_values = data['Active Power Mean'].tail(10).values

            # Calculate simple trend
            if len(recent_values) > 1:
                trend = (recent_values[-1] - recent_values[0]) / len(recent_values)
            else:
                trend = 0

            # Generate realistic varying predictions
            predictions = []
            current_value = recent_values[-1]

            for i in range(steps):
                # Create more realistic variations
                # Base trend component
                trend_component = trend * (i + 1)
                # Random walk component (each step affects the next)
                walk_component = np.random.normal(0, 150)
                # Cyclical component for more realistic behavior
                cyclical_component = 100 * np.sin(i * 0.2)
                # Noise component
                noise_component = np.random.normal(0, 100)

                # Calculate next value
                next_value = current_value + trend_component + walk_component + cyclical_component + noise_component

                # Keep within reasonable bounds but allow variation
                next_value = np.clip(next_value, self.target_min * 0.7, self.target_max * 1.3)
                predictions.append(next_value)

                # Update current value for next iteration (creates realistic progression)
                current_value = next_value * 0.9 + current_value * 0.1  # Smooth transition

            # Create future data structure similar to the original function
            future_data = data.copy()
            last_timestamp = data.index[-1]

            for i, pred_value in enumerate(predictions):
                new_timestamp = last_timestamp + timedelta(seconds=5*(i+1))
                new_row = pd.DataFrame({'Active Power Mean': [pred_value]}, index=[new_timestamp])
                future_data = pd.concat([future_data, new_row])

            return {
                'ML Predictions': predictions,
                'DL Predictions': [],
                'Future Data': future_data
            }

        except Exception as e:
            st.error(f"Error in simple predictions: {str(e)}")
            # Return dummy predictions
            predictions = [15000 + np.random.normal(0, 500) for _ in range(steps)]
            return {
                'ML Predictions': predictions,
                'DL Predictions': [],
                'Future Data': data
            }

    def generate_future_data_with_similarity(self, current_data, predictions, similarity=0.7):
        """Generate future data that is similar to predictions"""
        future_data = []
        ml_predictions = predictions['ML Predictions']

        last_timestamp = current_data.index[-1]

        for i, pred_value in enumerate(ml_predictions):
            new_timestamp = last_timestamp + timedelta(seconds=5*(i+1))

            # Generate new sensor values with some correlation to prediction
            # Use similarity factor to control how close actual values are to predictions
            noise_factor = 1 - similarity

            # Generate correlated values
            power_factor = np.random.normal(
                self.stats['Power Factor']['mean'],
                self.stats['Power Factor']['std'] * noise_factor
            )

            frequency = np.random.normal(
                self.stats['Frequency']['mean'],
                self.stats['Frequency']['std'] * noise_factor
            )

            voltage = np.random.normal(
                self.stats['Voltage L-L']['mean'],
                self.stats['Voltage L-L']['std'] * noise_factor
            )

            # Make active power similar to prediction
            actual_power = pred_value * similarity + np.random.normal(
                pred_value * (1 - similarity),
                self.stats['Active Power Mean']['std'] * noise_factor
            )

            future_data.append({
                'Timestamp': new_timestamp,
                'Power Factor': power_factor,
                'Frequency': frequency,
                'Voltage L-L': voltage,
                'Active Power Mean': actual_power,
                'Predicted_Power': pred_value
            })

        future_df = pd.DataFrame(future_data)
        future_df.set_index('Timestamp', inplace=True)
        return future_df


class DummyModel:
    """A dummy model that returns reasonable predictions when real models fail"""
    def __init__(self):
        self.mean_value = 15000  # Approximate mean from historical data

    def predict(self, X):
        """Return predictions based on input size"""
        if hasattr(X, 'shape'):
            return np.full(X.shape[0], self.mean_value + np.random.normal(0, 500, X.shape[0]))
        else:
            return np.array([self.mean_value + np.random.normal(0, 500)])


def main():
    st.markdown('<h1 class="main-header">🔮 Real-Time Sensor Prediction System</h1>', unsafe_allow_html=True)

    # Initialize simulators
    if 'simulator' not in st.session_state:
        st.session_state.simulator = SensorSimulator()

    if 'real_time_simulator' not in st.session_state:
        st.session_state.real_time_simulator = RealTimeSensorSimulator()

    simulator = st.session_state.simulator
    real_time_sim = st.session_state.real_time_simulator

    # Sidebar controls
    st.sidebar.header("🎛️ Live Monitoring Controls")

    # Real-time sensor monitoring toggle
    live_monitoring = st.sidebar.checkbox("🔴 Start Real-Time Sensors", value=False, help="Start continuous sensor simulation with real-time predictions")

    # Control the real-time sensor simulator
    if live_monitoring:
        if not real_time_sim.running:
            real_time_sim.start()
        st.sidebar.success("🟢 Real-time sensors active!")
        st.sidebar.metric("📊 Sensor Readings", len(real_time_sim.data_buffer))
        st.sidebar.metric("🔄 Thread Status", "RUNNING" if real_time_sim.running else "STOPPED")
        # Show latest reading time if available
        if len(real_time_sim.data_buffer) > 0:
            latest_time = real_time_sim.data_buffer[-1]['Timestamp']
            st.sidebar.metric("⏰ Latest Reading", latest_time.strftime('%H:%M:%S'))
    else:
        if real_time_sim.running:
            real_time_sim.stop()
        st.sidebar.info("⚪ Real-time sensors stopped")

    st.sidebar.markdown("---")

    # Alert thresholds
    st.sidebar.subheader("🚨 Alert Settings")

    power_threshold_high = st.sidebar.number_input(
        "High Power Alert (kW)",
        min_value=10000,
        max_value=25000,
        value=18000,
        step=500,
        help="Alert when predicted power exceeds this value"
    )

    power_threshold_low = st.sidebar.number_input(
        "Low Power Alert (kW)",
        min_value=5000,
        max_value=15000,
        value=12000,
        step=500,
        help="Alert when predicted power falls below this value"
    )

    prediction_accuracy_threshold = st.sidebar.slider(
        "Accuracy Alert Threshold (%)",
        min_value=50,
        max_value=95,
        value=60,
        step=5,
        help="Alert when prediction accuracy falls below this value"
    )

    st.sidebar.markdown("---")

    num_historical_points = st.sidebar.slider(
        "Historical Data Points",
        min_value=50,
        max_value=200,
        value=100,
        step=10
    )

    prediction_minutes = st.sidebar.slider(
        "Prediction Time (minutes)",
        min_value=1,
        max_value=5,
        value=2,
        step=1,
        help="How many minutes into the future to predict"
    )

    # Calculate prediction steps: 12 points per minute (every 5 seconds)
    prediction_steps = prediction_minutes * 12
    st.sidebar.write(f"Predicting {prediction_steps} points ({prediction_minutes} min @ 5-sec intervals)")

    noise_level = st.sidebar.slider(
        "Data Noise Level",
        min_value=0.01,
        max_value=0.5,
        value=0.1,
        step=0.01
    )

    prediction_similarity = st.sidebar.slider(
        "Prediction Accuracy (%)",
        min_value=50,
        max_value=95,
        value=70,
        step=5
    ) / 100

    # Initialize session state for live monitoring
    if 'live_data' not in st.session_state:
        st.session_state.live_data = pd.DataFrame()
        st.session_state.live_predictions = []
        st.session_state.alert_history = []
        st.session_state.last_update = datetime.now()

    # Manual data generation button
    if st.sidebar.button("🔄 Generate Initial Data"):
        # Generate initial historical data
        with st.spinner("Generating initial sensor data..."):
            current_data = simulator.generate_sensor_data(
                num_points=num_historical_points,
                noise_level=noise_level,
                interval_seconds=5
            )

        # Make initial predictions for all sensors
        with st.spinner("Making initial predictions..."):
            # Get predictions for all sensors
            all_sensor_predictions = simulator.predict_all_sensors(current_data, steps=prediction_steps)
            # Also get ML/DL predictions for power (for compatibility)
            predictions = simulator.make_prediction(current_data, steps=prediction_steps)

            if predictions and all_sensor_predictions:
                # Generate future data with controlled similarity
                future_data = simulator.generate_future_data_with_similarity(
                    current_data, predictions, similarity=prediction_similarity
                )

                # Store in session state
                st.session_state.predictions = predictions
                st.session_state.all_sensor_predictions = all_sensor_predictions
                st.session_state.future_data = future_data

        # Store in session state
        st.session_state.live_data = current_data
        st.session_state.last_update = datetime.now()
        st.success("✅ Initial data generated and predictions made!")

    # Real-time sensor data processing - only when refresh button is clicked
    if live_monitoring and real_time_sim.has_new_data():
        # This will be handled in the display section to avoid continuous updates
        pass

    # Legacy live monitoring logic (fallback)
    elif live_monitoring and hasattr(st.session_state, 'live_data') and not st.session_state.live_data.empty:
        # Initialize live monitoring state
        if 'live_monitoring_active' not in st.session_state:
            st.session_state.live_monitoring_active = True
            st.session_state.update_counter = 0

        # Check if enough time has passed for a new update
        current_time = datetime.now()
        time_since_last_live_update = (current_time - st.session_state.last_live_update).total_seconds()

        # Only update if enough time has passed (every 3 seconds)
        if time_since_last_live_update >= 3:
            # Generate new sensor reading
            last_timestamp = st.session_state.live_data.index[-1]
            new_reading = simulator.generate_single_sensor_reading(
                last_timestamp=last_timestamp,
                noise_level=noise_level
            )

            # Add new reading to live data
            new_row = pd.DataFrame([new_reading]).set_index('Timestamp')
            st.session_state.live_data = pd.concat([st.session_state.live_data, new_row])

            # Keep only recent data points
            if len(st.session_state.live_data) > num_historical_points:
                st.session_state.live_data = st.session_state.live_data.tail(num_historical_points)

            # Make predictions on current data
            predictions = simulator.make_prediction(st.session_state.live_data, steps=prediction_steps)

            if predictions:
                # Generate future data with controlled similarity
                future_data = simulator.generate_future_data_with_similarity(
                    st.session_state.live_data, predictions, similarity=prediction_similarity
                )

                # Store predictions
                st.session_state.predictions = predictions
                st.session_state.future_data = future_data

                # Check for alerts
                check_alerts(
                    new_reading,
                    predictions,
                    power_threshold_high,
                    power_threshold_low,
                    prediction_accuracy_threshold,
                    prediction_similarity
                )

            st.session_state.last_update = datetime.now()
            st.session_state.last_live_update = datetime.now()
            st.session_state.update_counter += 1

            # Controlled rerun - only when we actually added new data
            st.rerun()

    # Real-time sensor monitoring display
    if live_monitoring:
        # Show real-time status
        col1, col2, col3 = st.columns([2, 1, 1])
        with col1:
            st.markdown("### 🔴 **REAL-TIME SENSORS ACTIVE**")
            # Show latest sensor reading info
            if real_time_sim.has_new_data() and len(real_time_sim.data_buffer) > 0:
                latest_reading = real_time_sim.data_buffer[-1]
                st.caption(f"Latest: {latest_reading['Active Power Mean']:.0f} kW at {latest_reading['Timestamp'].strftime('%H:%M:%S')}")
        with col2:
            if st.button("🔄 Update Data", help="Get latest sensor data and update predictions"):
                # Force immediate data update
                st.session_state.last_data_update = datetime.now() - timedelta(seconds=10)  # Force immediate update
                st.rerun()
        with col3:
            # Show real-time status
            st.metric("📊 Sensor Buffer", len(real_time_sim.data_buffer))

            # Show time since last sensor reading
            if len(real_time_sim.data_buffer) > 0:
                latest_sensor_time = real_time_sim.data_buffer[-1]['Timestamp']
                seconds_ago = (datetime.now() - latest_sensor_time).total_seconds()
                st.caption(f"Last sensor: {seconds_ago:.0f}s ago")

            # Show if we have newer data available
            if hasattr(st.session_state, 'live_data') and not st.session_state.live_data.empty and len(real_time_sim.data_buffer) > 0:
                last_displayed = st.session_state.live_data.index[-1]
                latest_sensor = real_time_sim.data_buffer[-1]['Timestamp']
                if latest_sensor > last_displayed:
                    st.success("🆕 New data available!")
                else:
                    st.info("✅ Up to date")

        # Live data update instructions
        st.info("🔄 Real-time sensors are running in background. Click 'Update Data' to see latest readings, or refresh the page (F5) to see new data.")

    # Stop live monitoring when checkbox is unchecked
    elif not live_monitoring and 'live_monitoring_active' in st.session_state:
        st.session_state.live_monitoring_active = False

    # Display live monitoring status
    if live_monitoring:
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("🔴 Live Status", "MONITORING", delta="Active")
        with col2:
            if not st.session_state.live_data.empty:
                time_since_update = (datetime.now() - st.session_state.last_update).total_seconds()
                st.metric("⏱️ Last Update", f"{time_since_update:.1f}s ago")
            else:
                st.metric("⏱️ Last Update", "No data")
        with col3:
            if not st.session_state.live_data.empty:
                st.metric("📊 Data Points", len(st.session_state.live_data))
            else:
                st.metric("📊 Data Points", "0")

    # Display alerts
    if hasattr(st.session_state, 'alert_history') and st.session_state.alert_history:
        st.subheader("🚨 Recent Alerts")
        for alert in st.session_state.alert_history[-5:]:  # Show last 5 alerts
            if alert['type'] == 'high_power':
                st.error(f"⚠️ **HIGH POWER ALERT** at {alert['timestamp'].strftime('%H:%M:%S')}: {alert['value']:.1f} kW > {alert['threshold']} kW")
            elif alert['type'] == 'low_power':
                st.warning(f"⚠️ **LOW POWER ALERT** at {alert['timestamp'].strftime('%H:%M:%S')}: {alert['value']:.1f} kW < {alert['threshold']} kW")
            elif alert['type'] == 'low_accuracy':
                st.info(f"📉 **LOW ACCURACY ALERT** at {alert['timestamp'].strftime('%H:%M:%S')}: {alert['value']:.1f}% < {alert['threshold']}%")

    # Display results - show historical data with predictions
    if hasattr(st.session_state, 'live_data') and not st.session_state.live_data.empty:
        # Use existing historical data as base
        current_data = st.session_state.live_data

        # If real-time monitoring is active, check for new data and update
        if live_monitoring and real_time_sim.has_new_data():
            latest_real_time = real_time_sim.get_latest_readings(20)  # Get more readings
            if not latest_real_time.empty:
                # Check if we have newer data than what's currently displayed
                if hasattr(st.session_state, 'live_data') and not st.session_state.live_data.empty:
                    last_displayed_time = st.session_state.live_data.index[-1]
                    latest_sensor_time = latest_real_time.index[-1]

                    # Only update if we have newer data
                    if latest_sensor_time > last_displayed_time:
                        # Combine historical data with new real-time data
                        current_data = pd.concat([current_data, latest_real_time]).drop_duplicates()
                        # Keep only recent data (rolling window)
                        current_data = current_data.tail(num_historical_points)
                        # Update session state
                        st.session_state.live_data = current_data
                        st.session_state.last_update = datetime.now()

                        # Make new predictions on the updated data for ALL sensors
                        all_sensor_predictions = simulator.predict_all_sensors(current_data, steps=prediction_steps)
                        # Also get ML/DL predictions for power (for compatibility)
                        ml_predictions = simulator.make_prediction(current_data, steps=prediction_steps)

                        if all_sensor_predictions:
                            st.session_state.all_sensor_predictions = all_sensor_predictions
                            st.session_state.predictions = ml_predictions  # Keep for compatibility
                            # Generate future data
                            future_data = simulator.generate_future_data_with_similarity(
                                current_data, ml_predictions, similarity=prediction_similarity
                            )
                            st.session_state.future_data = future_data
                else:
                    # No existing data, use all real-time data
                    current_data = latest_real_time.tail(num_historical_points)
                    st.session_state.live_data = current_data
                    st.session_state.last_update = datetime.now()
        # Display combined historical + real-time data with predictions
        display_results(
            current_data,
            st.session_state.predictions if hasattr(st.session_state, 'predictions') else None,
            st.session_state.future_data if hasattr(st.session_state, 'future_data') else None,
            prediction_similarity,
            live_monitoring
        )
    else:
        st.info("👈 Click 'Generate Initial Data' to start, or enable 'Real-Time Sensors' for live monitoring!")


def check_alerts(new_reading, predictions, power_high, power_low, accuracy_threshold, current_accuracy):
    """Check for alert conditions and add to alert history"""
    current_power = new_reading['Active Power Mean']
    timestamp = new_reading['Timestamp']

    # Initialize alert history if not exists
    if 'alert_history' not in st.session_state:
        st.session_state.alert_history = []

    # Check power thresholds
    if current_power > power_high:
        alert = {
            'type': 'high_power',
            'timestamp': timestamp,
            'value': current_power,
            'threshold': power_high
        }
        st.session_state.alert_history.append(alert)

    elif current_power < power_low:
        alert = {
            'type': 'low_power',
            'timestamp': timestamp,
            'value': current_power,
            'threshold': power_low
        }
        st.session_state.alert_history.append(alert)

    # Check prediction accuracy
    accuracy_percent = current_accuracy * 100
    if accuracy_percent < accuracy_threshold:
        alert = {
            'type': 'low_accuracy',
            'timestamp': timestamp,
            'value': accuracy_percent,
            'threshold': accuracy_threshold
        }
        st.session_state.alert_history.append(alert)

    # Keep only recent alerts (last 50)
    if len(st.session_state.alert_history) > 50:
        st.session_state.alert_history = st.session_state.alert_history[-50:]


def display_results(current_data, predictions, future_data, similarity, live_mode=False):
    """Display the live prediction results like the reference image"""

    if predictions is None:
        st.warning("No predictions available yet. Generating predictions...")
        return

    # Live mode header
    if live_mode:
        st.markdown("### 🔴 **LIVE SENSOR PREDICTION MONITORING**")
        st.markdown(f"**Last Reading:** {current_data.index[-1].strftime('%H:%M:%S')} | **Next Update:** {(current_data.index[-1] + timedelta(seconds=5)).strftime('%H:%M:%S')}")
    else:
        st.markdown("### 📊 **SENSOR PREDICTION ANALYSIS**")

    # Quick metrics
    col1, col2, col3 = st.columns(3)

    with col1:
        current_power = current_data['Active Power Mean'].iloc[-1]
        live_indicator = "🔴 " if live_mode else ""
        st.metric(f"{live_indicator}Current Power", f"{current_power:.0f} kW")

    with col2:
        next_prediction = predictions['ML Predictions'][0]
        st.metric("🔮 Next Prediction", f"{next_prediction:.0f} kW")

    with col3:
        st.metric("📊 Data Points", len(current_data))

    # Multiple sensor charts
    st.subheader("📊 **All Sensor Data & Predictions**")

    # Create tabs for different views
    tab1, tab2 = st.tabs(["🔮 Active Power Predictions", "📊 All Sensors"])

    with tab1:
        st.markdown("### 🔮 **Live Active Power Predictions**")

        # Create a simple single chart like the reference image
        fig = go.Figure()

        # Show recent data for better visualization
        display_data = current_data.tail(100) if live_mode else current_data

        # Historical Data (blue solid line)
        fig.add_trace(
            go.Scatter(
                x=display_data.index,
                y=display_data['Active Power Mean'],
                name='Historical Data',
                line=dict(color='#1f77b4', width=2),  # Blue like reference
                mode='lines'
            )
        )

        # ML Predictions (red dashed line extending into future)
        if predictions is not None and 'ML Predictions' in predictions:
            # Create prediction timestamps starting from last data point
            last_time = current_data.index[-1]
            pred_times = [last_time + timedelta(seconds=5*(i+1)) for i in range(len(predictions['ML Predictions']))]

            fig.add_trace(
                go.Scatter(
                    x=pred_times,
                    y=predictions['ML Predictions'],
                    name='Predictions',
                    line=dict(color='#ff7f0e', dash='dash', width=2),  # Orange dashed like reference
                    mode='lines'
                )
            )

        # Style the chart like the reference image
        fig.update_layout(
            title=f"Future Active Power Mean Predictions ({len(predictions['ML Predictions'])//12:.1f} min @ 5-sec intervals)",
            xaxis_title="Time",
            yaxis_title="Active Power Mean (kW)",
            height=500,
            showlegend=True,
            template="plotly_white",
            legend=dict(
                x=0.02,
                y=0.98,
                bgcolor="rgba(255,255,255,0.8)",
                bordercolor="rgba(0,0,0,0.2)",
                borderwidth=1
            ),
            xaxis=dict(
                showgrid=True,
                gridcolor="lightgray",
                gridwidth=1
            ),
            yaxis=dict(
                showgrid=True,
                gridcolor="lightgray",
                gridwidth=1
            )
        )

        # Add live indicator if in live mode
        if live_mode:
            fig.add_annotation(
                text="🔴 LIVE",
                xref="paper", yref="paper",
                x=0.98, y=0.98,
                showarrow=False,
                font=dict(color="red", size=16, family="Arial Black"),
                bgcolor="white",
                bordercolor="red",
                borderwidth=2
            )

        st.plotly_chart(fig, use_container_width=True)

    with tab2:
        st.markdown("### 📊 **All Sensor Readings**")

        # Show recent data for better visualization
        display_data = current_data.tail(100) if live_mode else current_data

        # Create 2x2 grid for all sensors
        col1, col2 = st.columns(2)

        with col1:
            # Power Factor Chart with Predictions
            fig_pf = go.Figure()
            # Historical data
            fig_pf.add_trace(go.Scatter(
                x=display_data.index,
                y=display_data['Power Factor'],
                name='Historical',
                line=dict(color='#2E8B57', width=2),  # Sea Green
                mode='lines'
            ))
            # Predictions
            if hasattr(st.session_state, 'all_sensor_predictions') and 'Power Factor' in st.session_state.all_sensor_predictions:
                last_time = current_data.index[-1]
                pred_times = [last_time + timedelta(seconds=5*(i+1)) for i in range(len(st.session_state.all_sensor_predictions['Power Factor']))]
                fig_pf.add_trace(go.Scatter(
                    x=pred_times,
                    y=st.session_state.all_sensor_predictions['Power Factor'],
                    name='Predictions',
                    line=dict(color='#2E8B57', dash='dash', width=2),
                    mode='lines'
                ))
            fig_pf.update_layout(
                title="Power Factor (%) - Historical & Predictions",
                xaxis_title="Time",
                yaxis_title="Power Factor (%)",
                height=400,
                template="plotly_white"
            )
            if live_mode:
                fig_pf.add_annotation(
                    text="🔴 LIVE", xref="paper", yref="paper",
                    x=0.98, y=0.98, showarrow=False,
                    font=dict(color="red", size=12), bgcolor="white", bordercolor="red", borderwidth=1
                )
            st.plotly_chart(fig_pf, use_container_width=True)

            # Frequency Chart with Predictions
            fig_freq = go.Figure()
            # Historical data
            fig_freq.add_trace(go.Scatter(
                x=display_data.index,
                y=display_data['Frequency'],
                name='Historical',
                line=dict(color='#8A2BE2', width=2),  # Blue Violet
                mode='lines'
            ))
            # Predictions
            if hasattr(st.session_state, 'all_sensor_predictions') and 'Frequency' in st.session_state.all_sensor_predictions:
                last_time = current_data.index[-1]
                pred_times = [last_time + timedelta(seconds=5*(i+1)) for i in range(len(st.session_state.all_sensor_predictions['Frequency']))]
                fig_freq.add_trace(go.Scatter(
                    x=pred_times,
                    y=st.session_state.all_sensor_predictions['Frequency'],
                    name='Predictions',
                    line=dict(color='#8A2BE2', dash='dash', width=2),
                    mode='lines'
                ))
            fig_freq.update_layout(
                title="Frequency (Hz) - Historical & Predictions",
                xaxis_title="Time",
                yaxis_title="Frequency (Hz)",
                height=400,
                template="plotly_white"
            )
            if live_mode:
                fig_freq.add_annotation(
                    text="🔴 LIVE", xref="paper", yref="paper",
                    x=0.98, y=0.98, showarrow=False,
                    font=dict(color="red", size=12), bgcolor="white", bordercolor="red", borderwidth=1
                )
            st.plotly_chart(fig_freq, use_container_width=True)

        with col2:
            # Voltage Chart with Predictions
            fig_volt = go.Figure()
            # Historical data
            fig_volt.add_trace(go.Scatter(
                x=display_data.index,
                y=display_data['Voltage L-L'],
                name='Historical',
                line=dict(color='#FF6347', width=2),  # Tomato
                mode='lines'
            ))
            # Predictions
            if hasattr(st.session_state, 'all_sensor_predictions') and 'Voltage L-L' in st.session_state.all_sensor_predictions:
                last_time = current_data.index[-1]
                pred_times = [last_time + timedelta(seconds=5*(i+1)) for i in range(len(st.session_state.all_sensor_predictions['Voltage L-L']))]
                fig_volt.add_trace(go.Scatter(
                    x=pred_times,
                    y=st.session_state.all_sensor_predictions['Voltage L-L'],
                    name='Predictions',
                    line=dict(color='#FF6347', dash='dash', width=2),
                    mode='lines'
                ))
            fig_volt.update_layout(
                title="Voltage L-L (V) - Historical & Predictions",
                xaxis_title="Time",
                yaxis_title="Voltage (V)",
                height=400,
                template="plotly_white"
            )
            if live_mode:
                fig_volt.add_annotation(
                    text="🔴 LIVE", xref="paper", yref="paper",
                    x=0.98, y=0.98, showarrow=False,
                    font=dict(color="red", size=12), bgcolor="white", bordercolor="red", borderwidth=1
                )
            st.plotly_chart(fig_volt, use_container_width=True)

            # Active Power Chart with Predictions
            fig_power = go.Figure()
            # Historical data
            fig_power.add_trace(go.Scatter(
                x=display_data.index,
                y=display_data['Active Power Mean'],
                name='Historical',
                line=dict(color='#1f77b4', width=2),  # Blue
                mode='lines'
            ))
            # Predictions
            if hasattr(st.session_state, 'all_sensor_predictions') and 'Active Power Mean' in st.session_state.all_sensor_predictions:
                last_time = current_data.index[-1]
                pred_times = [last_time + timedelta(seconds=5*(i+1)) for i in range(len(st.session_state.all_sensor_predictions['Active Power Mean']))]
                fig_power.add_trace(go.Scatter(
                    x=pred_times,
                    y=st.session_state.all_sensor_predictions['Active Power Mean'],
                    name='Predictions',
                    line=dict(color='#1f77b4', dash='dash', width=2),
                    mode='lines'
                ))
            fig_power.update_layout(
                title="Active Power Mean (kW) - Historical & Predictions",
                xaxis_title="Time",
                yaxis_title="Power (kW)",
                height=400,
                template="plotly_white"
            )
            if live_mode:
                fig_power.add_annotation(
                    text="🔴 LIVE", xref="paper", yref="paper",
                    x=0.98, y=0.98, showarrow=False,
                    font=dict(color="red", size=12), bgcolor="white", bordercolor="red", borderwidth=1
                )
            st.plotly_chart(fig_power, use_container_width=True)


    # Simple prediction info
    if live_mode:
        st.subheader("🔮 **Live Prediction Info**")

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Next 5 Predictions", f"{len(predictions['ML Predictions'])} values")
            st.caption(f"({len(predictions['ML Predictions'])//12:.1f} min @ 5-sec intervals)")
        with col2:
            avg_pred = np.mean(predictions['ML Predictions'])
            st.metric("Average Prediction", f"{avg_pred:.0f} kW")
        with col3:
            pred_range = max(predictions['ML Predictions']) - min(predictions['ML Predictions'])
            st.metric("Prediction Range", f"{pred_range:.0f} kW")

    # Simple data view
    with st.expander("📊 **View Live Data & Predictions**"):
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**Latest Sensor Readings:**")
            recent_data = current_data.tail(10).copy()
            recent_data.index = recent_data.index.strftime('%H:%M:%S')
            st.dataframe(recent_data[['Active Power Mean']], use_container_width=True)

        with col2:
            st.markdown("**Next Predictions:**")
            if predictions:
                pred_times = [current_data.index[-1] + timedelta(seconds=5*(i+1)) for i in range(len(predictions['ML Predictions']))]
                pred_df = pd.DataFrame({
                    'Time': [t.strftime('%H:%M:%S') for t in pred_times],
                    'Predicted Power (kW)': [f"{p:.0f}" for p in predictions['ML Predictions']]
                })
                st.dataframe(pred_df, use_container_width=True, hide_index=True)


if __name__ == "__main__":
    main()
