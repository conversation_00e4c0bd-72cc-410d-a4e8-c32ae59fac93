import pandas as pd
import numpy as np
import joblib
import tensorflow as tf
from tensorflow.keras.models import load_model
import matplotlib.pyplot as plt
import os
import sys

# Add parent directory to path to import from other modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.preprocessing import load_data, create_features, normalize_data

def load_ml_model(model_path='models/best_ml_model.pkl'):
    """
    Load the saved ML model
    """
    return joblib.load(model_path)

def load_dl_model(model_path='models/best_dl_model.h5'):
    """
    Load the saved DL model
    """
    return load_model(model_path)

def prepare_data_for_prediction(data, target_col, sequence_length=24, min_vals=None, max_vals=None):
    """
    Prepare data for prediction
    """
    # Create features
    df_features = create_features(data)
    
    # If min_vals and max_vals are provided, normalize the data
    if min_vals is not None and max_vals is not None:
        df_features_scaled = (df_features - min_vals) / (max_vals - min_vals)
    else:
        df_features_scaled = df_features
    
    # For ML models, return the last row (keep as DataFrame to preserve feature names)
    ml_input = df_features_scaled.iloc[-1:]
    
    # For DL models, prepare sequence data
    if len(df_features_scaled) >= sequence_length:
        dl_input = df_features_scaled.iloc[-sequence_length:].values.reshape(1, sequence_length, df_features_scaled.shape[1])
    else:
        dl_input = None
    
    return ml_input, dl_input, df_features

def predict_next_value(data, ml_model, dl_model=None, target_col='Active Power Mean', 
                       sequence_length=24, min_vals=None, max_vals=None, target_min=None, target_max=None):
    """
    Predict the next value using both ML and DL models
    """
    # Prepare data for prediction
    ml_input, dl_input, features = prepare_data_for_prediction(
        data, target_col, sequence_length, min_vals, max_vals
    )
    
    # Make predictions with ML model
    ml_prediction = ml_model.predict(ml_input)[0]
    
    # Make predictions with DL model if provided
    if dl_model is not None and dl_input is not None:
        dl_prediction = dl_model.predict(dl_input)[0][0]
    else:
        dl_prediction = None
    
    # If target scaling values are provided, denormalize the predictions
    if target_min is not None and target_max is not None:
        ml_prediction = ml_prediction * (target_max - target_min) + target_min
        if dl_prediction is not None:
            dl_prediction = dl_prediction * (target_max - target_min) + target_min
    
    return {
        'ML Prediction': ml_prediction,
        'DL Prediction': dl_prediction
    }

def predict_future_values(data, ml_model, dl_model=None, target_col='Active Power Mean', 
                          steps=24, sequence_length=24, min_vals=None, max_vals=None, 
                          target_min=None, target_max=None):
    """
    Predict multiple future values
    """
    # Make a copy of the data
    future_data = data.copy()
    
    ml_predictions = []
    dl_predictions = []
    
    for _ in range(steps):
        # Predict next value
        predictions = predict_next_value(
            future_data, ml_model, dl_model, target_col, 
            sequence_length, min_vals, max_vals, target_min, target_max
        )
        
        # Store predictions
        ml_predictions.append(predictions['ML Prediction'])
        if predictions['DL Prediction'] is not None:
            dl_predictions.append(predictions['DL Prediction'])
        
        # Create a new row with the predicted value
        last_timestamp = future_data.index[-1]
        new_timestamp = last_timestamp + pd.Timedelta(seconds=5)  # 5-second intervals for live prediction
        
        new_row = pd.DataFrame({target_col: [ml_predictions[-1]]}, index=[new_timestamp])
        
        # Append the new row to the data
        future_data = pd.concat([future_data, new_row])
    
    return {
        'ML Predictions': ml_predictions,
        'DL Predictions': dl_predictions,
        'Future Data': future_data
    }

def plot_predictions(original_data, future_predictions, target_col='Active Power Mean', save_dir='results/plots'):
    """
    Plot the original data and future predictions
    """
    os.makedirs(save_dir, exist_ok=True)
    
    plt.figure(figsize=(15, 8))
    
    # Plot original data
    plt.plot(original_data.index, original_data[target_col], label='Historical Data')
    
    # Plot ML predictions
    future_data = future_predictions['Future Data']
    future_indices = future_data.index[-len(future_predictions['ML Predictions']):]
    plt.plot(future_indices, future_predictions['ML Predictions'], label='ML Predictions', linestyle='--')
    
    # Plot DL predictions if available
    if len(future_predictions['DL Predictions']) > 0:
        plt.plot(future_indices, future_predictions['DL Predictions'], label='DL Predictions', linestyle=':')
    
    plt.title(f'Future {target_col} Predictions')
    plt.xlabel('Time')
    plt.ylabel(target_col)
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.savefig(f'{save_dir}/future_predictions.png')
    plt.close()

def save_predictions_to_csv(original_data, future_predictions, target_col='Active Power Mean', save_dir='results'):
    """
    Save the predictions to a CSV file
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # Create a dataframe with the predictions
    future_indices = future_predictions['Future Data'].index[-len(future_predictions['ML Predictions']):]
    
    predictions_df = pd.DataFrame({
        'Timestamp': future_indices,
        f'{target_col}_ML_Prediction': future_predictions['ML Predictions']
    })
    
    if len(future_predictions['DL Predictions']) > 0:
        predictions_df[f'{target_col}_DL_Prediction'] = future_predictions['DL Predictions']
    
    # Save to CSV
    predictions_df.to_csv(f'{save_dir}/future_predictions.csv', index=False)
    
    return predictions_df

if __name__ == "__main__":
    # This will be executed when the script is run directly
    print("Inference module loaded successfully!")
