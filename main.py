import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import joblib
import tensorflow as tf
from sklearn.model_selection import train_test_split

# Import modules
from src.data.preprocessing import (
    load_data, explore_data, create_features,
    prepare_train_test_data, normalize_data, prepare_sequence_data
)
from src.models.ml_models import (
    train_linear_models, train_tree_based_models,
    evaluate_models, save_best_model, feature_importance
)
from src.models.simple_dl import train_lstm_model, compare_ml_dl
from src.evaluation.metrics import create_summary_report

def main():
    print("Starting the regression model pipeline...")

    # Create necessary directories
    os.makedirs('models', exist_ok=True)
    os.makedirs('results/plots', exist_ok=True)

    # Step 1: Load and preprocess data
    print("\n--- Step 1: Loading and preprocessing data ---")
    df = load_data("Feuil1-Table 1.csv")
    print(f"Data loaded successfully! Shape: {df.shape}")

    # Step 2: Explore data
    print("\n--- Step 2: Exploring data ---")
    corr_matrix = explore_data(df)
    print("Exploratory analysis completed and plots saved!")

    # Step 3: Create features
    print("\n--- Step 3: Creating features ---")
    # Create a simpler version with fewer features
    df_features = df.copy()

    # Add basic time features
    df_features['hour'] = df_features.index.hour
    df_features['day'] = df_features.index.day
    df_features['day_of_week'] = df_features.index.dayofweek
    df_features['is_weekend'] = df_features['day_of_week'].apply(lambda x: 1 if x >= 5 else 0)

    # Add simple lag features for the target
    target_col = 'Active Power Mean'
    for lag in [1, 2, 3]:
        df_features[f'{target_col}_lag_{lag}'] = df_features[target_col].shift(lag)

    # Drop rows with NaN values
    df_features.dropna(inplace=True)

    print(f"Features created! New shape: {df_features.shape}")

    # Step 4: Prepare train/test data for 'Active Power Mean' prediction
    print("\n--- Step 4: Preparing train/test data ---")

    # Create target variable (shifted by 1 step)
    df_features[f'{target_col}_target'] = df_features[target_col].shift(-1)
    df_features.dropna(inplace=True)

    # Split into features and target
    X = df_features.drop([f'{target_col}_target'], axis=1)
    y = df_features[f'{target_col}_target']

    # Split into train and test sets (time-based split)
    test_size = 0.2
    split_idx = int(len(df_features) * (1 - test_size))
    X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
    y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]

    print(f"Train/test data prepared! X_train shape: {X_train.shape}, X_test shape: {X_test.shape}")

    # Step 5: Normalize data
    print("\n--- Step 5: Normalizing data ---")
    # Calculate min and max values from training data
    min_vals = X_train.min()
    max_vals = X_train.max()

    # Apply min-max scaling
    X_train_scaled = (X_train - min_vals) / (max_vals - min_vals)
    X_test_scaled = (X_test - min_vals) / (max_vals - min_vals)

    # Save min_vals and max_vals for later use in inference
    joblib.dump((min_vals, max_vals), 'models/scaling_values.pkl')

    # Get target min and max for denormalization
    target_min = y_train.min()
    target_max = y_train.max()
    joblib.dump((target_min, target_max), 'models/target_scaling.pkl')

    print("Data normalized and scaling values saved!")

    # Step 6: Train ML models
    print("\n--- Step 6: Training ML models ---")

    # Train linear models
    linear_models = train_linear_models(X_train_scaled, y_train)
    print("Linear models trained!")

    # Train tree-based models
    tree_models = train_tree_based_models(X_train_scaled, y_train)
    print("Tree-based models trained!")

    # Combine all models
    all_ml_models = {**linear_models, **tree_models}

    # Step 7: Evaluate ML models
    print("\n--- Step 7: Evaluating ML models ---")
    ml_results = evaluate_models(all_ml_models, X_test_scaled, y_test)
    print("ML models evaluated!")

    # Step 8: Save best ML model
    print("\n--- Step 8: Saving best ML model ---")
    best_ml_model_name, best_ml_model = save_best_model(all_ml_models, ml_results)

    # Plot feature importance for the best model
    feature_importance({best_ml_model_name: best_ml_model}, X_train.columns, save_dir='results/plots/feature_importance')

    # Get the best ML model metrics
    best_ml_idx = ml_results['RMSE'].idxmin()
    best_ml_rmse = ml_results.loc[best_ml_idx, 'RMSE']
    best_ml_r2 = ml_results.loc[best_ml_idx, 'R2']

    # Step 9: Train deep learning model
    print("\n--- Step 9: Training deep learning model ---")
    sequence_length = 10  # Use 10 time steps
    lstm_model, lstm_rmse, lstm_r2 = train_lstm_model(
        X_train_scaled, y_train, X_test_scaled, y_test,
        sequence_length=sequence_length, epochs=50, batch_size=32
    )
    print("LSTM model trained and evaluated!")

    # Step 10: Compare ML and DL models
    print("\n--- Step 10: Comparing ML and DL models ---")
    comparison = compare_ml_dl(
        best_ml_rmse, best_ml_r2, lstm_rmse, lstm_r2, best_ml_model_name
    )
    print("ML and DL models compared!")

    print("\nRegression model pipeline completed successfully!")

if __name__ == "__main__":
    main()
