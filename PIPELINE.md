# Time Series Regression Pipeline Documentation

This document provides a detailed overview of the time series regression pipeline implemented in this project. The pipeline is designed to predict future values of sensor data based on historical measurements.

## Table of Contents

1. [Data Overview](#data-overview)
2. [Pipeline Architecture](#pipeline-architecture)
3. [Data Preprocessing](#data-preprocessing)
4. [Feature Engineering](#feature-engineering)
5. [Model Training](#model-training)
6. [Model Evaluation](#model-evaluation)
7. [Model Comparison](#model-comparison)
8. [Inference](#inference)
9. [Results](#results)
10. [Future Improvements](#future-improvements)

## Data Overview

The dataset consists of time series sensor data with the following features:
- Timestamp
- Power Factor
- Frequency
- Voltage L-L
- Active Power Mean

The data is sampled at 15-minute intervals and contains measurements from industrial sensors.

## Pipeline Architecture

The pipeline is structured as follows:

```
├── data/                  # Data storage
├── models/                # Saved models
├── results/               # Results and evaluation metrics
│   └── plots/             # Visualizations and plots
├── src/                   # Source code
│   ├── data/              # Data processing modules
│   ├── models/            # ML and DL model implementations
│   ├── evaluation/        # Evaluation metrics and visualization
│   └── inference/         # Inference and prediction modules
├── main.py                # Main script to run the pipeline
├── predict_future.py      # Script for making predictions
└── README.md              # Project documentation
```

## Data Preprocessing

The data preprocessing steps include:

1. **Loading the data**: The CSV file is loaded and parsed to handle the specific format with timestamps.
2. **Cleaning**: Column names are cleaned, and any empty columns are removed.
3. **Timestamp conversion**: The timestamp column is converted to a datetime format and set as the index.
4. **Exploratory Data Analysis**: Basic statistics and visualizations are generated to understand the data distribution and relationships.

```python
# Example of data loading and preprocessing
def load_data(file_path):
    # Read the CSV file with custom handling for the specific format
    with open(file_path, 'r') as f:
        lines = f.readlines()
    
    # Parse header
    header_line = lines[0].strip()
    headers = [h.strip() for h in header_line.split(',') if h.strip()]
    
    # Parse data rows
    data = []
    for line in lines[1:]:
        values = [v.strip() for v in line.split(',') if v.strip()]
        if len(values) >= len(headers):
            row_data = {}
            for i, header in enumerate(headers):
                row_data[header] = values[i]
            data.append(row_data)
    
    # Create DataFrame
    df = pd.DataFrame(data)
    
    # Convert numeric columns
    for col in df.columns:
        if col != 'Timestamp':
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # Convert timestamp to datetime
    df['Timestamp'] = pd.to_datetime(df['Timestamp'], format='%d/%m/%Y@%H:%M:%S.%f')
    
    # Set timestamp as index
    df.set_index('Timestamp', inplace=True)
    
    return df
```

## Feature Engineering

Feature engineering is a critical step in improving model performance. The following features are created:

1. **Time-based features**:
   - Hour of day
   - Day of month
   - Day of week
   - Weekend indicator

2. **Lag features**:
   - Previous values (t-1, t-2, t-3)
   - These capture the temporal dependencies in the data

```python
# Example of feature engineering
def create_features(df):
    # Create a copy to avoid modifying the original
    df_features = df.copy()
    
    # Add time-based features
    df_features['hour'] = df_features.index.hour
    df_features['day'] = df_features.index.day
    df_features['day_of_week'] = df_features.index.dayofweek
    df_features['is_weekend'] = df_features['day_of_week'].apply(lambda x: 1 if x >= 5 else 0)
    
    # Add lag features for the target
    target_col = 'Active Power Mean'
    for lag in [1, 2, 3]:
        df_features[f'{target_col}_lag_{lag}'] = df_features[target_col].shift(lag)
    
    # Drop rows with NaN values
    df_features.dropna(inplace=True)
    
    return df_features
```

## Model Training

The pipeline implements and compares two types of models:

### Machine Learning Models

1. **Linear Models**:
   - Linear Regression
   - Ridge Regression
   - Lasso Regression

2. **Tree-based Models**:
   - Random Forest
   - Gradient Boosting
   - XGBoost

```python
# Example of training ML models
def train_linear_models(X_train, y_train):
    models = {
        'Linear Regression': LinearRegression(),
        'Ridge Regression': Ridge(alpha=1.0),
        'Lasso Regression': Lasso(alpha=0.1)
    }
    
    trained_models = {}
    for name, model in models.items():
        model.fit(X_train, y_train)
        trained_models[name] = model
    
    return trained_models

def train_tree_based_models(X_train, y_train):
    models = {
        'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42),
        'Gradient Boosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
        'XGBoost': XGBRegressor(n_estimators=100, random_state=42)
    }
    
    trained_models = {}
    for name, model in models.items():
        model.fit(X_train, y_train)
        trained_models[name] = model
    
    return trained_models
```

### Deep Learning Model

An LSTM (Long Short-Term Memory) neural network is implemented for sequence prediction:

```python
# Example of creating and training an LSTM model
def create_lstm_model(input_shape, units=50):
    model = Sequential([
        LSTM(units, input_shape=input_shape),
        Dropout(0.2),
        Dense(20, activation='relu'),
        Dense(1)
    ])
    
    model.compile(optimizer='adam', loss='mse')
    
    return model

def train_lstm_model(X_train, y_train, X_test, y_test, sequence_length=10, epochs=50, batch_size=32):
    # Prepare sequence data
    X_train_seq, y_train_seq = prepare_sequence_data(X_train, y_train, sequence_length)
    X_test_seq, y_test_seq = prepare_sequence_data(X_test, y_test, sequence_length)
    
    # Create model
    input_shape = (sequence_length, X_train.shape[1])
    model = create_lstm_model(input_shape)
    
    # Train model with early stopping
    early_stopping = EarlyStopping(
        monitor='val_loss',
        patience=10,
        restore_best_weights=True
    )
    
    history = model.fit(
        X_train_seq, y_train_seq,
        validation_data=(X_test_seq, y_test_seq),
        epochs=epochs,
        batch_size=batch_size,
        callbacks=[early_stopping],
        verbose=1
    )
    
    # Evaluate and return model
    y_pred = model.predict(X_test_seq)
    mse = mean_squared_error(y_test_seq, y_pred)
    rmse = np.sqrt(mse)
    r2 = r2_score(y_test_seq, y_pred)
    
    return model, rmse, r2
```

## Model Evaluation

Models are evaluated using the following metrics:

1. **Root Mean Square Error (RMSE)**: Measures the average magnitude of the errors
2. **Mean Absolute Error (MAE)**: Measures the average absolute difference between predictions and actual values
3. **R-squared (R²)**: Indicates the proportion of the variance in the dependent variable that is predictable from the independent variables

```python
# Example of model evaluation
def evaluate_models(models, X_test, y_test):
    results = {
        'Model': [],
        'MSE': [],
        'RMSE': [],
        'MAE': [],
        'R2': []
    }
    
    for name, model in models.items():
        # Make predictions
        y_pred = model.predict(X_test)
        
        # Calculate metrics
        mse = mean_squared_error(y_test, y_pred)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        
        # Store results
        results['Model'].append(name)
        results['MSE'].append(mse)
        results['RMSE'].append(rmse)
        results['MAE'].append(mae)
        results['R2'].append(r2)
    
    return pd.DataFrame(results)
```

## Model Comparison

The pipeline compares the performance of traditional machine learning models with deep learning models:

```python
# Example of model comparison
def compare_ml_dl(ml_rmse, ml_r2, dl_rmse, dl_r2, ml_name):
    # Create comparison dataframe
    comparison = pd.DataFrame({
        'Model': [ml_name, 'LSTM'],
        'RMSE': [ml_rmse, dl_rmse],
        'R2': [ml_r2, dl_r2]
    })
    
    # Create summary report
    with open('results/ml_dl_summary.txt', 'w') as f:
        f.write("===== MODEL COMPARISON SUMMARY =====\n\n")
        
        f.write(f"ML Model: {ml_name}\n")
        f.write(f"RMSE: {ml_rmse:.4f}\n")
        f.write(f"R2: {ml_r2:.4f}\n\n")
        
        f.write("DL Model: LSTM\n")
        f.write(f"RMSE: {dl_rmse:.4f}\n")
        f.write(f"R2: {dl_r2:.4f}\n\n")
        
        if dl_rmse < ml_rmse:
            f.write("The Deep Learning model (LSTM) performed better based on RMSE.\n")
        else:
            f.write(f"The Machine Learning model ({ml_name}) performed better based on RMSE.\n")
    
    return comparison
```

## Inference

The inference process uses the best trained model to predict future values:

```python
# Example of inference
def predict_next_values(data, ml_model, steps=24, target_col='Active Power Mean'):
    # Make a copy of the data
    future_data = data.copy()
    
    # Load scaling values
    min_vals, max_vals = joblib.load('models/scaling_values.pkl')
    
    predictions = []
    
    for _ in range(steps):
        # Prepare features for the current state
        current_features = future_data.iloc[-1:].copy()
        
        # Add time features
        current_features['hour'] = current_features.index.hour
        current_features['day'] = current_features.index.day
        current_features['day_of_week'] = current_features.index.dayofweek
        current_features['is_weekend'] = current_features['day_of_week'].apply(lambda x: 1 if x >= 5 else 0)
        
        # Add lag features
        for lag in [1, 2, 3]:
            if len(future_data) > lag:
                current_features[f'{target_col}_lag_{lag}'] = future_data[target_col].iloc[-lag]
            else:
                current_features[f'{target_col}_lag_{lag}'] = future_data[target_col].iloc[-1]
        
        # Scale features
        current_features_scaled = (current_features - min_vals) / (max_vals - min_vals)
        current_features_scaled = current_features_scaled.fillna(0)
        
        # Make prediction
        prediction = ml_model.predict(current_features_scaled)[0]
        predictions.append(prediction)
        
        # Create a new row with the predicted value
        last_timestamp = future_data.index[-1]
        new_timestamp = last_timestamp + pd.Timedelta(minutes=15)
        
        new_row = pd.DataFrame({target_col: [prediction]}, index=[new_timestamp])
        
        # Append the new row to the data
        future_data = pd.concat([future_data, new_row])
    
    return predictions, future_data
```

## Results

The evaluation results show:

1. **Best Machine Learning Model**: Gradient Boosting
   - RMSE: 1917.48
   - R²: 0.87

2. **Deep Learning Model (LSTM)**:
   - RMSE: 2066.43
   - R²: 0.84

The Gradient Boosting model outperformed the LSTM model for this particular dataset and prediction task.

## Future Improvements

Potential improvements to the pipeline include:

1. **More advanced feature engineering**:
   - Fourier features for capturing seasonality
   - More sophisticated lag features
   - External features (e.g., weather data)

2. **Hyperparameter tuning**:
   - Grid search or Bayesian optimization for model parameters
   - Learning rate scheduling for deep learning models

3. **Ensemble methods**:
   - Stacking multiple models
   - Weighted averaging of predictions

4. **More sophisticated deep learning architectures**:
   - Bidirectional LSTM
   - Attention mechanisms
   - Transformer models

5. **Uncertainty quantification**:
   - Prediction intervals
   - Monte Carlo dropout for uncertainty estimation

6. **Online learning**:
   - Updating models as new data becomes available
   - Drift detection and adaptation
