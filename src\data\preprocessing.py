import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os

def load_data(file_path):
    """
    Load the CSV data and perform initial preprocessing
    """
    # Read the CSV file with custom handling for the specific format
    # The file has double commas that need special handling
    with open(file_path, 'r') as f:
        lines = f.readlines()

    # Parse header
    header_line = lines[0].strip()
    headers = [h.strip() for h in header_line.split(',') if h.strip()]

    # Parse data rows
    data = []
    for line in lines[1:]:
        values = [v.strip() for v in line.split(',') if v.strip()]
        if len(values) >= len(headers):  # Ensure we have enough values
            row_data = {}
            for i, header in enumerate(headers):
                row_data[header] = values[i]
            data.append(row_data)

    # Create DataFrame
    df = pd.DataFrame(data)

    # Convert numeric columns
    for col in df.columns:
        if col != 'Timestamp':
            df[col] = pd.to_numeric(df[col], errors='coerce')

    # Convert timestamp to datetime
    df['Timestamp'] = pd.to_datetime(df['Timestamp'], format='%d/%m/%Y@%H:%M:%S.%f')

    # Set timestamp as index
    df.set_index('Timestamp', inplace=True)

    return df

def explore_data(df, save_dir='results/plots'):
    """
    Perform exploratory data analysis and save plots
    """
    # Create directory if it doesn't exist
    os.makedirs(save_dir, exist_ok=True)

    # Plot time series for each feature
    plt.figure(figsize=(15, 10))
    for i, col in enumerate(df.columns):
        plt.subplot(len(df.columns), 1, i+1)
        plt.plot(df.index, df[col])
        plt.title(f'Time Series: {col}')
        plt.tight_layout()
    plt.savefig(f'{save_dir}/time_series_plots.png')
    plt.close()

    # Plot distribution of each feature
    plt.figure(figsize=(15, 10))
    for i, col in enumerate(df.columns):
        plt.subplot(len(df.columns), 1, i+1)
        sns.histplot(df[col], kde=True)
        plt.title(f'Distribution: {col}')
        plt.tight_layout()
    plt.savefig(f'{save_dir}/distribution_plots.png')
    plt.close()

    # Plot correlation matrix
    plt.figure(figsize=(10, 8))
    correlation_matrix = df.corr()
    sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm')
    plt.title('Correlation Matrix')
    plt.tight_layout()
    plt.savefig(f'{save_dir}/correlation_matrix.png')
    plt.close()

    # Return correlation matrix for feature selection
    return correlation_matrix

def create_features(df):
    """
    Create time-based features and lag features
    """
    # Copy the dataframe to avoid modifying the original
    df_features = df.copy()

    # Add time-based features
    df_features['hour'] = df_features.index.hour
    df_features['day'] = df_features.index.day
    df_features['month'] = df_features.index.month
    df_features['day_of_week'] = df_features.index.dayofweek
    df_features['is_weekend'] = df_features['day_of_week'].apply(lambda x: 1 if x >= 5 else 0)

    # Create a dictionary to store all new features
    new_features = {}

    # Create lag features for each column (t-1, t-2, t-3, t-6, t-12)
    for col in df.columns:
        for lag in [1, 2, 3, 6, 12]:
            new_features[f'{col}_lag_{lag}'] = df_features[col].shift(lag)

    # Create rolling window features (mean and std only)
    for col in df.columns:
        for window in [3, 6, 12]:
            new_features[f'{col}_rolling_mean_{window}'] = df_features[col].rolling(window=window).mean()
            new_features[f'{col}_rolling_std_{window}'] = df_features[col].rolling(window=window).std()

    # Combine all features at once
    df_features = pd.concat([df_features, pd.DataFrame(new_features)], axis=1)

    # Drop rows with NaN values (due to lag and rolling features)
    df_features.dropna(inplace=True)

    return df_features

def prepare_train_test_data(df, target_col, test_size=0.2, forecast_horizon=1):
    """
    Prepare training and testing datasets for time series forecasting

    Args:
        df: DataFrame with features
        target_col: Column to predict
        test_size: Proportion of data to use for testing
        forecast_horizon: Number of steps ahead to forecast
    """
    # Make a copy to avoid modifying the original
    df_copy = df.copy()

    # Create target variable (shifted by forecast_horizon)
    df_copy[f'{target_col}_target'] = df_copy[target_col].shift(-forecast_horizon)

    # Drop rows with NaN in target
    df_copy.dropna(subset=[f'{target_col}_target'], inplace=True)

    # Check if dataframe is empty after dropping NaN values
    if len(df_copy) == 0:
        raise ValueError("No data left after dropping NaN values. Check your data or reduce forecast_horizon.")

    # Split into features and target
    X = df_copy.drop(f'{target_col}_target', axis=1)
    y = df_copy[f'{target_col}_target']

    # Split into train and test sets (time-based split)
    split_idx = int(len(df_copy) * (1 - test_size))
    X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
    y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]

    return X_train, X_test, y_train, y_test

def normalize_data(X_train, X_test):
    """
    Normalize the data using min-max scaling
    """
    # Calculate min and max values from training data
    min_vals = X_train.min()
    max_vals = X_train.max()

    # Apply min-max scaling
    X_train_scaled = (X_train - min_vals) / (max_vals - min_vals)
    X_test_scaled = (X_test - min_vals) / (max_vals - min_vals)

    return X_train_scaled, X_test_scaled, min_vals, max_vals

def prepare_sequence_data(X, y, sequence_length):
    """
    Prepare sequence data for deep learning models (LSTM/GRU)
    """
    X_seq, y_seq = [], []

    for i in range(len(X) - sequence_length):
        X_seq.append(X.iloc[i:i+sequence_length].values)
        y_seq.append(y.iloc[i+sequence_length])

    return np.array(X_seq), np.array(y_seq)

if __name__ == "__main__":
    # Test the preprocessing functions
    df = load_data("Feuil1-Table 1.csv")
    print("Data loaded successfully!")
    print(f"Shape: {df.shape}")
    print(f"Columns: {df.columns}")
    print(df.head())

    # Explore data
    corr_matrix = explore_data(df)
    print("Exploratory analysis completed and plots saved!")

    # Create features
    df_features = create_features(df)
    print(f"Features created! New shape: {df_features.shape}")

    # Prepare train/test data for 'Active Power Mean' prediction
    X_train, X_test, y_train, y_test = prepare_train_test_data(
        df_features, 'Active Power Mean', test_size=0.2, forecast_horizon=1
    )
    print(f"Train/test data prepared! X_train shape: {X_train.shape}, X_test shape: {X_test.shape}")

    # Normalize data
    X_train_scaled, X_test_scaled, _, _ = normalize_data(X_train, X_test)
    print("Data normalized!")

    # Prepare sequence data for deep learning
    X_train_seq, y_train_seq = prepare_sequence_data(X_train_scaled, y_train, sequence_length=24)
    X_test_seq, y_test_seq = prepare_sequence_data(X_test_scaled, y_test, sequence_length=24)
    print(f"Sequence data prepared! X_train_seq shape: {X_train_seq.shape}")
