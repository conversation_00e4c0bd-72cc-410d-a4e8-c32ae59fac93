#!/usr/bin/env python3
"""
Simplified model retraining script to fix compatibility issues.
This script retrains the models with the current scikit-learn version.
"""

import os
import pandas as pd
import numpy as np
import joblib
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import train_test_split

# Import custom modules
from src.data.preprocessing import load_data, create_features, prepare_train_test_data, normalize_data

def train_models():
    """Train and save models with current scikit-learn version"""
    print("🔄 Retraining models with current scikit-learn version...")
    
    # Create directories
    os.makedirs('models', exist_ok=True)
    
    # Load and prepare data
    print("📊 Loading data...")
    df = load_data("Feuil1-Table 1.csv")
    print(f"Data loaded! Shape: {df.shape}")
    
    # Create features
    print("🔧 Creating features...")
    df_features = create_features(df)
    print(f"Features created! Shape: {df_features.shape}")
    
    # Prepare train/test data
    print("📈 Preparing train/test data...")
    X_train, X_test, y_train, y_test = prepare_train_test_data(
        df_features, 'Active Power Mean', test_size=0.2, forecast_horizon=1
    )
    
    # Normalize data
    print("🔄 Normalizing data...")
    X_train_scaled, X_test_scaled, min_vals, max_vals = normalize_data(X_train, X_test)
    
    # Calculate target scaling
    target_min = y_train.min()
    target_max = y_train.max()
    
    # Train multiple models
    models = {
        'Linear Regression': LinearRegression(),
        'Ridge Regression': Ridge(alpha=1.0),
        'Lasso Regression': Lasso(alpha=0.1),
        'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42),
        'Gradient Boosting': GradientBoostingRegressor(n_estimators=100, random_state=42)
    }
    
    print("🤖 Training models...")
    trained_models = {}
    results = []
    
    for name, model in models.items():
        print(f"  Training {name}...")
        try:
            model.fit(X_train_scaled, y_train)
            
            # Make predictions
            y_pred = model.predict(X_test_scaled)
            
            # Calculate metrics
            mse = mean_squared_error(y_test, y_pred)
            rmse = np.sqrt(mse)
            r2 = r2_score(y_test, y_pred)
            
            trained_models[name] = model
            results.append({
                'Model': name,
                'RMSE': rmse,
                'R2': r2
            })
            
            print(f"    ✅ {name}: RMSE={rmse:.2f}, R2={r2:.4f}")
            
        except Exception as e:
            print(f"    ❌ Failed to train {name}: {str(e)}")
    
    # Find best model
    if results:
        results_df = pd.DataFrame(results)
        best_idx = results_df['RMSE'].idxmin()
        best_model_name = results_df.loc[best_idx, 'Model']
        best_model = trained_models[best_model_name]
        best_rmse = results_df.loc[best_idx, 'RMSE']
        best_r2 = results_df.loc[best_idx, 'R2']
        
        print(f"\n🏆 Best model: {best_model_name}")
        print(f"   RMSE: {best_rmse:.2f}")
        print(f"   R2: {best_r2:.4f}")
        
        # Save the best model
        print("💾 Saving models and scaling values...")
        joblib.dump(best_model, 'models/best_ml_model.pkl')
        joblib.dump((min_vals, max_vals), 'models/scaling_values.pkl')
        joblib.dump((target_min, target_max), 'models/target_scaling.pkl')
        
        # Save model info
        with open('models/best_ml_model_info.txt', 'w') as f:
            f.write(f"Best Model: {best_model_name}\n")
            f.write(f"RMSE: {best_rmse}\n")
            f.write(f"R2: {best_r2}\n")
        
        print("✅ Models saved successfully!")
        return True
    else:
        print("❌ No models were successfully trained!")
        return False

def train_simple_lstm():
    """Train a simple LSTM model"""
    try:
        import tensorflow as tf
        from tensorflow.keras.models import Sequential
        from tensorflow.keras.layers import LSTM, Dense, Dropout
        from tensorflow.keras.callbacks import EarlyStopping
        
        print("🧠 Training LSTM model...")
        
        # Load and prepare data
        df = load_data("Feuil1-Table 1.csv")
        df_features = create_features(df)
        
        # Prepare train/test data
        X_train, X_test, y_train, y_test = prepare_train_test_data(
            df_features, 'Active Power Mean', test_size=0.2, forecast_horizon=1
        )
        
        # Normalize data
        X_train_scaled, X_test_scaled, min_vals, max_vals = normalize_data(X_train, X_test)
        
        # Prepare sequence data for LSTM
        sequence_length = 24
        X_train_seq, y_train_seq = [], []
        X_test_seq, y_test_seq = [], []
        
        # Create sequences for training
        for i in range(sequence_length, len(X_train_scaled)):
            X_train_seq.append(X_train_scaled.iloc[i-sequence_length:i].values)
            y_train_seq.append(y_train.iloc[i])
        
        # Create sequences for testing
        for i in range(sequence_length, len(X_test_scaled)):
            X_test_seq.append(X_test_scaled.iloc[i-sequence_length:i].values)
            y_test_seq.append(y_test.iloc[i])
        
        X_train_seq = np.array(X_train_seq)
        y_train_seq = np.array(y_train_seq)
        X_test_seq = np.array(X_test_seq)
        y_test_seq = np.array(y_test_seq)
        
        if len(X_train_seq) > 0:
            # Create LSTM model
            model = Sequential([
                LSTM(50, input_shape=(sequence_length, X_train_scaled.shape[1])),
                Dropout(0.2),
                Dense(20, activation='relu'),
                Dense(1)
            ])
            
            model.compile(optimizer='adam', loss='mse')
            
            # Train model
            early_stopping = EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True)
            
            history = model.fit(
                X_train_seq, y_train_seq,
                validation_data=(X_test_seq, y_test_seq),
                epochs=50,
                batch_size=32,
                callbacks=[early_stopping],
                verbose=1
            )
            
            # Save model
            model.save('models/lstm_model.h5')
            print("✅ LSTM model saved successfully!")
            return True
        else:
            print("❌ Not enough data for LSTM training")
            return False
            
    except Exception as e:
        print(f"❌ Error training LSTM: {str(e)}")
        return False

def main():
    """Main function to retrain all models"""
    print("🚀 Starting model retraining...")
    
    # Train ML models
    ml_success = train_models()
    
    # Train LSTM model
    lstm_success = train_simple_lstm()
    
    if ml_success:
        print("\n🎉 Model retraining completed successfully!")
        print("✅ ML models retrained and saved")
        if lstm_success:
            print("✅ LSTM model retrained and saved")
        else:
            print("⚠️ LSTM model training failed, but ML models are working")
        print("\n🌐 You can now run the Streamlit app without compatibility issues!")
    else:
        print("\n❌ Model retraining failed!")
        return False
    
    return True

if __name__ == "__main__":
    main()
