import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
import joblib
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, <PERSON>, <PERSON>so
from sklearn.svm import SVR
from sklearn.neighbors import KNeighborsRegressor
from xgboost import XGBRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.model_selection import GridSearchCV, RandomizedSearchCV

def train_linear_models(X_train, y_train):
    """
    Train linear regression models
    """
    models = {
        'Linear Regression': LinearRegression(),
        'Ridge Regression': Ridge(alpha=1.0),
        'Lasso Regression': Lasso(alpha=0.1)
    }

    trained_models = {}
    for name, model in models.items():
        print(f"Training {name}...")
        model.fit(X_train, y_train)
        trained_models[name] = model

    return trained_models

def train_tree_based_models(X_train, y_train):
    """
    Train tree-based regression models
    """
    models = {
        'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42),
        'Gradient Boosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
        'XGBoost': XGBRegressor(n_estimators=100, random_state=42)
    }

    trained_models = {}
    for name, model in models.items():
        print(f"Training {name}...")
        model.fit(X_train, y_train)
        trained_models[name] = model

    return trained_models



def evaluate_models(models, X_test, y_test, save_dir='results/plots'):
    """
    Evaluate models and return performance metrics
    """
    os.makedirs(save_dir, exist_ok=True)

    results = {
        'Model': [],
        'MSE': [],
        'RMSE': [],
        'MAE': [],
        'R2': []
    }

    plt.figure(figsize=(15, 10))

    for i, (name, model) in enumerate(models.items()):
        # Make predictions
        y_pred = model.predict(X_test)

        # Calculate metrics
        mse = mean_squared_error(y_test, y_pred)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)

        # Store results
        results['Model'].append(name)
        results['MSE'].append(mse)
        results['RMSE'].append(rmse)
        results['MAE'].append(mae)
        results['R2'].append(r2)

        # Plot actual vs predicted
        plt.subplot(len(models), 1, i+1)
        plt.plot(y_test.values, label='Actual')
        plt.plot(y_pred, label='Predicted')
        plt.title(f'{name} - RMSE: {rmse:.2f}, R2: {r2:.2f}')
        plt.legend()
        plt.tight_layout()

    # Save the plot
    plt.savefig(f'{save_dir}/ml_models_predictions.png')
    plt.close()

    # Create a comparison plot
    plt.figure(figsize=(12, 8))

    # Plot RMSE comparison
    plt.subplot(2, 1, 1)
    sns.barplot(x='Model', y='RMSE', data=pd.DataFrame(results))
    plt.title('RMSE Comparison')
    plt.xticks(rotation=45)

    # Plot R2 comparison
    plt.subplot(2, 1, 2)
    sns.barplot(x='Model', y='R2', data=pd.DataFrame(results))
    plt.title('R2 Comparison')
    plt.xticks(rotation=45)

    plt.tight_layout()
    plt.savefig(f'{save_dir}/ml_models_comparison.png')
    plt.close()

    return pd.DataFrame(results)



def save_best_model(models, results, save_dir='models'):
    """
    Save the best performing model based on RMSE
    """
    os.makedirs(save_dir, exist_ok=True)

    # Find the best model based on RMSE
    best_model_name = results.loc[results['RMSE'].idxmin(), 'Model']
    best_model = models[best_model_name]

    # Save the model
    joblib.dump(best_model, f'{save_dir}/best_ml_model.pkl')

    # Save model info
    with open(f'{save_dir}/best_ml_model_info.txt', 'w') as f:
        f.write(f"Best Model: {best_model_name}\n")
        f.write(f"RMSE: {results.loc[results['RMSE'].idxmin(), 'RMSE']}\n")
        f.write(f"R2: {results.loc[results['RMSE'].idxmin(), 'R2']}\n")

    print(f"Best model ({best_model_name}) saved to {save_dir}/best_ml_model.pkl")

    return best_model_name, best_model

def feature_importance(models, feature_names, save_dir='results/plots'):
    """
    Plot feature importance for tree-based models
    """
    os.makedirs(save_dir, exist_ok=True)

    for name, model in models.items():
        # Check if model has feature_importances_ attribute
        if hasattr(model, 'feature_importances_'):
            # Get feature importances
            importances = model.feature_importances_

            # Sort feature importances in descending order
            indices = np.argsort(importances)[::-1]

            # Plot feature importances
            plt.figure(figsize=(12, 8))
            plt.title(f'Feature Importances - {name}')
            plt.bar(range(len(indices)), importances[indices], align='center')
            plt.xticks(range(len(indices)), [feature_names[i] for i in indices], rotation=90)
            plt.tight_layout()
            plt.savefig(f'{save_dir}/feature_importance_{name}.png')
            plt.close()

if __name__ == "__main__":
    # This will be executed when the script is run directly
    print("ML models module loaded successfully!")
