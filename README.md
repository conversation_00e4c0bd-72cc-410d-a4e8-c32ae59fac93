# Time Series Regression for Sensor Data

This project implements a comprehensive machine learning and deep learning pipeline for time series regression on sensor data. The goal is to predict future values of various metrics based on historical data.

## Project Structure

```
├── data/                  # Data storage
├── models/                # Saved models
├── results/               # Results and evaluation metrics
│   └── plots/             # Visualizations and plots
├── src/                   # Source code
│   ├── data/              # Data processing modules
│   ├── models/            # ML and DL model implementations
│   ├── evaluation/        # Evaluation metrics and visualization
│   └── inference/         # Inference and prediction modules
├── main.py                # Main script to run the pipeline
└── README.md              # Project documentation
```

## Features

- **Data Processing**:
  - Time series data loading and cleaning
  - Feature engineering (time-based features, lag features, rolling statistics)
  - Data normalization and sequence preparation

- **Machine Learning Models**:
  - Linear models (Linear Regression, Ridge, Lasso)
  - Tree-based models (Random Forest, Gradient Boosting, XGBoost)
  - Other models (SVR, KNN)
  - Hyperparameter tuning

- **Deep Learning Models**:
  - LSTM (Long Short-Term Memory)
  - GRU (Gated Recurrent Unit)
  - Bidirectional LSTM
  - Early stopping and learning rate reduction

- **Evaluation**:
  - Comprehensive metrics (RMSE, MAE, R²)
  - Visualization of predictions
  - Feature importance analysis
  - Model comparison

- **Inference**:
  - Future value prediction
  - Visualization of forecasts

## How to Run

1. Ensure you have all the required dependencies installed:
   ```
   pip install pandas numpy matplotlib seaborn scikit-learn xgboost tensorflow joblib
   ```

2. Run the main script:
   ```
   python main.py
   ```

3. Check the results in the `results/` directory:
   - Model evaluation metrics
   - Comparison plots
   - Feature importance visualizations
   - Future predictions

## Data

The project uses time series data with the following features:
- Power Factor
- Frequency
- Voltage L-L
- Active Power Mean

The data is sampled at 15-minute intervals.

## Model Selection

The pipeline trains multiple models and selects the best one based on RMSE (Root Mean Square Error). The best model is saved for future inference.

## Future Predictions

The system can predict future values for a specified number of time steps. Both the best ML and DL models are used for prediction, and the results are compared.

## Customization

You can customize the pipeline by modifying the following parameters in `main.py`:
- Target column to predict
- Test set size
- Forecast horizon
- Sequence length for deep learning models
- Number of future steps to predict
