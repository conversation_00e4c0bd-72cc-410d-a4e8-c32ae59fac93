<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔮 Real-Time Sensor Prediction System</title>
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-start {
            background: #4CAF50;
            color: white;
        }
        
        .btn-stop {
            background: #f44336;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .status {
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }
        
        .metrics {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .metric {
            background: rgba(255,255,255,0.1);
            padding: 15px 25px;
            border-radius: 12px;
            text-align: center;
            backdrop-filter: blur(10px);
            min-width: 120px;
        }
        
        .metric-value {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .charts-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .chart-wrapper {
            background: rgba(255,255,255,0.95);
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .chart-title {
            color: #333;
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .live-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            background: #ff4444;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.3; }
            100% { opacity: 1; }
        }
        
        .footer {
            text-align: center;
            margin-top: 30px;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .charts-container {
                grid-template-columns: 1fr;
            }
            
            .metrics {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔮 Real-Time Sensor Prediction System</h1>
            <p>Live streaming sensor data with ML predictions</p>
        </div>
        
        <div class="controls">
            <button class="btn btn-start" onclick="startSensors()">🚀 Start Live Sensors</button>
            <button class="btn btn-stop" onclick="stopSensors()">🛑 Stop Sensors</button>
        </div>
        
        <div class="status" id="status">
            🔌 Connecting to sensor system...
        </div>
        
        <div class="metrics">
            <div class="metric">
                <div class="metric-value" id="dataPoints">0</div>
                <div class="metric-label">Data Points</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="latestPower">0</div>
                <div class="metric-label">Latest Power (kW)</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="latestTime">--:--:--</div>
                <div class="metric-label">Last Update</div>
            </div>
        </div>
        
        <div class="charts-container">
            <div class="chart-wrapper">
                <div class="chart-title">
                    <span class="live-indicator" id="powerIndicator" style="display:none;"></span>
                    ⚡ Active Power Mean (kW)
                </div>
                <div id="powerChart" style="height: 400px;"></div>
            </div>
            
            <div class="chart-wrapper">
                <div class="chart-title">
                    <span class="live-indicator" id="frequencyIndicator" style="display:none;"></span>
                    📊 Frequency (Hz)
                </div>
                <div id="frequencyChart" style="height: 400px;"></div>
            </div>
            
            <div class="chart-wrapper">
                <div class="chart-title">
                    <span class="live-indicator" id="voltageIndicator" style="display:none;"></span>
                    🔌 Voltage L-L (V)
                </div>
                <div id="voltageChart" style="height: 400px;"></div>
            </div>
            
            <div class="chart-wrapper">
                <div class="chart-title">
                    <span class="live-indicator" id="powerFactorIndicator" style="display:none;"></span>
                    📈 Power Factor (%)
                </div>
                <div id="powerFactorChart" style="height: 400px;"></div>
            </div>
        </div>
        
        <div class="footer">
            <p>🔄 Real-time updates via WebSocket | 🤖 ML-powered predictions</p>
        </div>
    </div>

    <script>
        // WebSocket connection
        const socket = io();
        let isLive = false;
        
        // Socket event handlers
        socket.on('connect', function() {
            console.log('Connected to server');
            document.getElementById('status').innerHTML = '✅ Connected to sensor system';
        });
        
        socket.on('disconnect', function() {
            console.log('Disconnected from server');
            document.getElementById('status').innerHTML = '❌ Disconnected from sensor system';
            isLive = false;
            updateLiveIndicators();
        });
        
        socket.on('status', function(data) {
            document.getElementById('status').innerHTML = '📡 ' + data.message;
            if (data.message.includes('started')) {
                isLive = true;
            } else if (data.message.includes('stopped')) {
                isLive = false;
            }
            updateLiveIndicators();
        });
        
        socket.on('sensor_update', function(data) {
            console.log('Received sensor update:', data.latest_reading);
            updateCharts(data.historical, data.predictions);
            updateMetrics(data.latest_reading, data.data_points);
        });
        
        // Control functions
        function startSensors() {
            socket.emit('start_sensors');
        }
        
        function stopSensors() {
            socket.emit('stop_sensors');
        }
        
        function updateLiveIndicators() {
            const indicators = ['powerIndicator', 'frequencyIndicator', 'voltageIndicator', 'powerFactorIndicator'];
            indicators.forEach(id => {
                const element = document.getElementById(id);
                element.style.display = isLive ? 'inline-block' : 'none';
            });
        }
        
        function updateMetrics(latestReading, dataPoints) {
            document.getElementById('dataPoints').textContent = dataPoints;
            document.getElementById('latestPower').textContent = Math.round(latestReading.active_power);
            
            const time = new Date(latestReading.timestamp);
            document.getElementById('latestTime').textContent = time.toLocaleTimeString();
        }
        
        function updateCharts(historical, predictions) {
            updateChart('powerChart', 'Active Power (kW)', historical.active_power, predictions.active_power, historical.timestamps, predictions.timestamps, '#1f77b4');
            updateChart('frequencyChart', 'Frequency (Hz)', historical.frequency, predictions.frequency, historical.timestamps, predictions.timestamps, '#ff7f0e');
            updateChart('voltageChart', 'Voltage (V)', historical.voltage_ll, predictions.voltage_ll, historical.timestamps, predictions.timestamps, '#2ca02c');
            updateChart('powerFactorChart', 'Power Factor (%)', historical.power_factor, predictions.power_factor, historical.timestamps, predictions.timestamps, '#d62728');
        }
        
        function updateChart(chartId, title, historicalData, predictionData, historicalTimes, predictionTimes, color) {
            const traces = [
                {
                    x: historicalTimes,
                    y: historicalData,
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Historical',
                    line: { color: color, width: 2 }
                },
                {
                    x: predictionTimes,
                    y: predictionData,
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Predictions',
                    line: { color: color, width: 2, dash: 'dash' }
                }
            ];
            
            const layout = {
                title: title,
                xaxis: { title: 'Time' },
                yaxis: { title: title },
                showlegend: true,
                margin: { t: 50, r: 50, b: 50, l: 80 },
                plot_bgcolor: 'white',
                paper_bgcolor: 'white'
            };
            
            Plotly.newPlot(chartId, traces, layout, {responsive: true});
        }
    </script>
</body>
</html>
